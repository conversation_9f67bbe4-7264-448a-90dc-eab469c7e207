PGDMP  *                    }            sirev    16.10 (Debian 16.10-1.pgdg13+1)    16.10 (Debian 16.10-1.pgdg13+1) .    �
           0    0    ENCODING    ENCODING        SET client_encoding = 'UTF8';
                      false            �
           0    0 
   STDSTRINGS 
   STDSTRINGS     (   SET standard_conforming_strings = 'on';
                      false            �
           0    0 
   SEARCHPATH 
   SEARCHPATH     8   SELECT pg_catalog.set_config('search_path', '', false);
                      false            �
           1262    16384    sirev    DATABASE     p   CREATE DATABASE sirev WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.utf8';
    DROP DATABASE sirev;
                postgres    false                        3079    16394    pgcrypto 	   EXTENSION     <   CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA public;
    DROP EXTENSION pgcrypto;
                   false            �
           0    0    EXTENSION pgcrypto    COMMENT     <   COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';
                        false    2            �            1259    16389    alembic_version    TABLE     X   CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);
 #   DROP TABLE public.alembic_version;
       public         heap    postgres    false            �            1259    16520    hotel_amenities    TABLE       CREATE TABLE public.hotel_amenities (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    hotel_id uuid NOT NULL,
    name character varying(200) NOT NULL,
    provider character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);
 #   DROP TABLE public.hotel_amenities;
       public         heap    postgres    false            �            1259    16506    hotel_images    TABLE     N  CREATE TABLE public.hotel_images (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    hotel_id uuid NOT NULL,
    url character varying(1000) NOT NULL,
    width integer,
    height integer,
    attribution character varying(500),
    provider character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);
     DROP TABLE public.hotel_images;
       public         heap    postgres    false            �            1259    16532 
   hotel_reviews    TABLE     �  CREATE TABLE public.hotel_reviews (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    hotel_id uuid NOT NULL,
    review_id character varying(200),
    reviewer_name character varying(200),
    rating double precision,
    text text NOT NULL,
    date date,
    url character varying(1000),
    provider character varying(50) NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);
 !   DROP TABLE public.hotel_reviews;
       public         heap    postgres    false            �            1259    16463    hotels    TABLE     *  CREATE TABLE public.hotels (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(300) NOT NULL,
    source_hotel_id character varying(200),
    address character varying(500),
    city character varying(120),
    country character varying(100),
    description text,
    ranking_out_of integer,
    ranking integer,
    rating double precision,
    num_reviews integer,
    subratings json,
    amenities json,
    web_url character varying(500),
    see_all_photos character varying(500),
    video_link character varying(500)
);
    DROP TABLE public.hotels;
       public         heap    postgres    false            �            1259    16488    share_link_trips    TABLE     $  CREATE TABLE public.share_link_trips (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    share_link_id uuid NOT NULL,
    trip_id uuid NOT NULL,
    "position" integer NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
 $   DROP TABLE public.share_link_trips;
       public         heap    postgres    false            �            1259    16446    share_links    TABLE     X  CREATE TABLE public.share_links (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid NOT NULL,
    token character varying(140) NOT NULL,
    title character varying(200),
    note text,
    is_active boolean DEFAULT true NOT NULL,
    expires_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);
    DROP TABLE public.share_links;
       public         heap    postgres    false            �            1259    16473    trips    TABLE     �  CREATE TABLE public.trips (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    hotel_id uuid NOT NULL,
    nights integer,
    tour_operator character varying(50),
    outbound_date character varying(20),
    outbound_dep_time character varying(20),
    outbound_flight character varying(20),
    outbound_arr_time character varying(20),
    return_date character varying(20),
    return_dep_time character varying(20),
    return_flight character varying(20),
    return_arr_time character varying(20),
    comment text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    room_desc text,
    people_per_trip character varying(100),
    full_price double precision,
    price double precision
);
    DROP TABLE public.trips;
       public         heap    postgres    false            �            1259    16431    users    TABLE     �  CREATE TABLE public.users (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    email character varying(320) NOT NULL,
    google_sub character varying(64),
    name character varying(200),
    avatar_url character varying(500),
    is_allowed boolean DEFAULT false NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);
    DROP TABLE public.users;
       public         heap    postgres    false            �
          0    16389    alembic_version 
   TABLE DATA           6   COPY public.alembic_version (version_num) FROM stdin;
    public          postgres    false    216            �
          0    16520    hotel_amenities 
   TABLE DATA           S   COPY public.hotel_amenities (id, hotel_id, name, provider, created_at) FROM stdin;
    public          postgres    false    223            �
          0    16506    hotel_images 
   TABLE DATA           k   COPY public.hotel_images (id, hotel_id, url, width, height, attribution, provider, created_at) FROM stdin;
    public          postgres    false    222            �
          0    16532 
   hotel_reviews 
   TABLE DATA           ~   COPY public.hotel_reviews (id, hotel_id, review_id, reviewer_name, rating, text, date, url, provider, created_at) FROM stdin;
    public          postgres    false    224            �
          0    16463    hotels 
   TABLE DATA           �   COPY public.hotels (id, name, source_hotel_id, address, city, country, description, ranking_out_of, ranking, rating, num_reviews, subratings, amenities, web_url, see_all_photos, video_link) FROM stdin;
    public          postgres    false    219            �
          0    16488    share_link_trips 
   TABLE DATA           j   COPY public.share_link_trips (id, share_link_id, trip_id, "position", created_at, updated_at) FROM stdin;
    public          postgres    false    221            �
          0    16446    share_links 
   TABLE DATA           i   COPY public.share_links (id, user_id, token, title, note, is_active, expires_at, created_at) FROM stdin;
    public          postgres    false    218            �
          0    16473    trips 
   TABLE DATA             COPY public.trips (id, hotel_id, nights, tour_operator, outbound_date, outbound_dep_time, outbound_flight, outbound_arr_time, return_date, return_dep_time, return_flight, return_arr_time, comment, created_at, updated_at, room_desc, people_per_trip, full_price, price) FROM stdin;
    public          postgres    false    220            �
          0    16431    users 
   TABLE DATA           l   COPY public.users (id, email, google_sub, name, avatar_url, is_allowed, created_at, updated_at) FROM stdin;
    public          postgres    false    217            
           2606    16393 #   alembic_version alembic_version_pkc 
   CONSTRAINT     j   ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);
 M   ALTER TABLE ONLY public.alembic_version DROP CONSTRAINT alembic_version_pkc;
       public            postgres    false    216            4
           2606    16526 $   hotel_amenities hotel_amenities_pkey 
   CONSTRAINT     b   ALTER TABLE ONLY public.hotel_amenities
    ADD CONSTRAINT hotel_amenities_pkey PRIMARY KEY (id);
 N   ALTER TABLE ONLY public.hotel_amenities DROP CONSTRAINT hotel_amenities_pkey;
       public            postgres    false    223            2
           2606    16514    hotel_images hotel_images_pkey 
   CONSTRAINT     \   ALTER TABLE ONLY public.hotel_images
    ADD CONSTRAINT hotel_images_pkey PRIMARY KEY (id);
 H   ALTER TABLE ONLY public.hotel_images DROP CONSTRAINT hotel_images_pkey;
       public            postgres    false    222            6
           2606    16540     hotel_reviews hotel_reviews_pkey 
   CONSTRAINT     ^   ALTER TABLE ONLY public.hotel_reviews
    ADD CONSTRAINT hotel_reviews_pkey PRIMARY KEY (id);
 J   ALTER TABLE ONLY public.hotel_reviews DROP CONSTRAINT hotel_reviews_pkey;
       public            postgres    false    224            (
           2606    16470    hotels hotels_pkey 
   CONSTRAINT     P   ALTER TABLE ONLY public.hotels
    ADD CONSTRAINT hotels_pkey PRIMARY KEY (id);
 <   ALTER TABLE ONLY public.hotels DROP CONSTRAINT hotels_pkey;
       public            postgres    false    219            0
           2606    16495 &   share_link_trips share_link_trips_pkey 
   CONSTRAINT     d   ALTER TABLE ONLY public.share_link_trips
    ADD CONSTRAINT share_link_trips_pkey PRIMARY KEY (id);
 P   ALTER TABLE ONLY public.share_link_trips DROP CONSTRAINT share_link_trips_pkey;
       public            postgres    false    221            $
           2606    16455    share_links share_links_pkey 
   CONSTRAINT     Z   ALTER TABLE ONLY public.share_links
    ADD CONSTRAINT share_links_pkey PRIMARY KEY (id);
 F   ALTER TABLE ONLY public.share_links DROP CONSTRAINT share_links_pkey;
       public            postgres    false    218            &
           2606    16457 !   share_links share_links_token_key 
   CONSTRAINT     ]   ALTER TABLE ONLY public.share_links
    ADD CONSTRAINT share_links_token_key UNIQUE (token);
 K   ALTER TABLE ONLY public.share_links DROP CONSTRAINT share_links_token_key;
       public            postgres    false    218            .
           2606    16482    trips trips_pkey 
   CONSTRAINT     N   ALTER TABLE ONLY public.trips
    ADD CONSTRAINT trips_pkey PRIMARY KEY (id);
 :   ALTER TABLE ONLY public.trips DROP CONSTRAINT trips_pkey;
       public            postgres    false    220            ,
           2606    16553     hotels uq_hotels_source_hotel_id 
   CONSTRAINT     f   ALTER TABLE ONLY public.hotels
    ADD CONSTRAINT uq_hotels_source_hotel_id UNIQUE (source_hotel_id);
 J   ALTER TABLE ONLY public.hotels DROP CONSTRAINT uq_hotels_source_hotel_id;
       public            postgres    false    219            
           2606    16443    users users_email_key 
   CONSTRAINT     Q   ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);
 ?   ALTER TABLE ONLY public.users DROP CONSTRAINT users_email_key;
       public            postgres    false    217             
           2606    16445    users users_google_sub_key 
   CONSTRAINT     [   ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_google_sub_key UNIQUE (google_sub);
 D   ALTER TABLE ONLY public.users DROP CONSTRAINT users_google_sub_key;
       public            postgres    false    217            "
           2606    16441    users users_pkey 
   CONSTRAINT     N   ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);
 :   ALTER TABLE ONLY public.users DROP CONSTRAINT users_pkey;
       public            postgres    false    217            )
           1259    16471    ix_hotels_name    INDEX     A   CREATE INDEX ix_hotels_name ON public.hotels USING btree (name);
 "   DROP INDEX public.ix_hotels_name;
       public            postgres    false    219            *
           1259    16472    ix_hotels_source_hotel_id    INDEX     W   CREATE INDEX ix_hotels_source_hotel_id ON public.hotels USING btree (source_hotel_id);
 -   DROP INDEX public.ix_hotels_source_hotel_id;
       public            postgres    false    219            <
           2606    16527 -   hotel_amenities hotel_amenities_hotel_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.hotel_amenities
    ADD CONSTRAINT hotel_amenities_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES public.hotels(id) ON DELETE CASCADE;
 W   ALTER TABLE ONLY public.hotel_amenities DROP CONSTRAINT hotel_amenities_hotel_id_fkey;
       public          postgres    false    3368    223    219            ;
           2606    16515 '   hotel_images hotel_images_hotel_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.hotel_images
    ADD CONSTRAINT hotel_images_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES public.hotels(id) ON DELETE CASCADE;
 Q   ALTER TABLE ONLY public.hotel_images DROP CONSTRAINT hotel_images_hotel_id_fkey;
       public          postgres    false    222    219    3368            =
           2606    16541 )   hotel_reviews hotel_reviews_hotel_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.hotel_reviews
    ADD CONSTRAINT hotel_reviews_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES public.hotels(id) ON DELETE CASCADE;
 S   ALTER TABLE ONLY public.hotel_reviews DROP CONSTRAINT hotel_reviews_hotel_id_fkey;
       public          postgres    false    219    224    3368            9
           2606    16496 4   share_link_trips share_link_trips_share_link_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.share_link_trips
    ADD CONSTRAINT share_link_trips_share_link_id_fkey FOREIGN KEY (share_link_id) REFERENCES public.share_links(id) ON DELETE CASCADE;
 ^   ALTER TABLE ONLY public.share_link_trips DROP CONSTRAINT share_link_trips_share_link_id_fkey;
       public          postgres    false    3364    218    221            :
           2606    16501 .   share_link_trips share_link_trips_trip_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.share_link_trips
    ADD CONSTRAINT share_link_trips_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trips(id) ON DELETE CASCADE;
 X   ALTER TABLE ONLY public.share_link_trips DROP CONSTRAINT share_link_trips_trip_id_fkey;
       public          postgres    false    220    3374    221            7
           2606    16458 $   share_links share_links_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.share_links
    ADD CONSTRAINT share_links_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
 N   ALTER TABLE ONLY public.share_links DROP CONSTRAINT share_links_user_id_fkey;
       public          postgres    false    218    217    3362            8
           2606    16483    trips trips_hotel_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.trips
    ADD CONSTRAINT trips_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES public.hotels(id) ON DELETE CASCADE;
 C   ALTER TABLE ONLY public.trips DROP CONSTRAINT trips_hotel_id_fkey;
       public          postgres    false    3368    220    219            �
   $   x�32025040�O��+K-*�/(�LN����� w�      �
   
   x������ � �      �
   
   x������ � �      �
   
   x������ � �      �
   
   x������ � �      �
   
   x������ � �      �
   
   x������ � �      �
   
   x������ � �      �
   �   x�}��N�0 ��s��I��_��3"b�B����[�5>=�x�~�3]�5�JT�UP��@���V�r��rM�L(�07�! G@�o��)�)ŕ��3
vx��c�s~OuYϐ�х�����S�|���|l���m�8|47ǣe����</��f���A�����u��j{�IKhAS����0����DDQA���WT�
H�     