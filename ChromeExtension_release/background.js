// Service Worker for Chrome Extension Auth
console.log('Service worker loaded');

// Auth configuration
const AUTH_CONFIG = {
  apiBaseUrl: 'https://proposal.toptravelagency.ca',
  clientId: '456832608708-7g4m3l80bf4eujjpdqveg5efge75pkhh.apps.googleusercontent.com',
  redirectUri: 'https://glecjnlgconheeinilkfeojkfafdflpd.chromiumapp.org'
};

// Generate a random nonce for security
function generateNonce() {
  const array = new Uint32Array(8);
  crypto.getRandomValues(array);
  return Array.from(array, dec => dec.toString(16)).join('');
}

// Build Google OAuth URL
function buildGoogleAuthUrl(nonce) {
  const params = new URLSearchParams({
    client_id: AUTH_CONFIG.clientId,
    redirect_uri: AUTH_CONFIG.redirectUri,
    response_type: 'id_token',
    scope: 'openid email profile',
    nonce: nonce
  });
  return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
}

// Extract ID token from URL fragment
function extractIdTokenFromUrl(url) {
  const fragment = new URL(url).hash.substring(1);
  const params = new URLSearchParams(fragment);
  return params.get('id_token');
}

// Verify nonce matches
function verifyNonce(idToken, expectedNonce) {
  try {
    // Decode JWT payload (base64url decode)
    const payload = JSON.parse(atob(idToken.split('.')[1].replace(/-/g, '+').replace(/_/g, '/')));
    return payload.nonce === expectedNonce;
  } catch (error) {
    console.error('Error verifying nonce:', error);
    return false;
  }
}

// Verify ID token with backend
async function verifyWithBackend(idToken) {
  const response = await fetch(`${AUTH_CONFIG.apiBaseUrl}/auth/google/verify`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ id_token: idToken })
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.detail || 'Backend verification failed');
  }

  return await response.json();
}

// Store authentication data
async function storeAuthData(authResponse) {
  const tokenData = {
    accessToken: authResponse.access_token,
    refreshToken: authResponse.refresh_token,
    expiresAt: Date.now() + (authResponse.expires_in * 1000),
    user: authResponse.user,
    isAuthenticated: true
  };

  await chrome.storage.local.set({ authData: tokenData });
  return tokenData;
}

// Main authentication function
async function authenticate() {
  try {
    console.log('Starting OAuth flow in service worker...');
    
    const nonce = generateNonce();
    const authUrl = buildGoogleAuthUrl(nonce);
    
    // Launch web auth flow
    const responseUrl = await new Promise((resolve, reject) => {
      chrome.identity.launchWebAuthFlow({
        url: authUrl,
        interactive: true
      }, (responseUrl) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(responseUrl);
        }
      });
    });

    if (!responseUrl) {
      throw new Error('No response URL received');
    }

    // Extract ID token
    const idToken = extractIdTokenFromUrl(responseUrl);
    if (!idToken) {
      throw new Error('No ID token found in response');
    }

    // Verify nonce
    if (!verifyNonce(idToken, nonce)) {
      throw new Error('Nonce verification failed');
    }

    console.log('ID token obtained and verified');

    // Send to backend for verification
    const authResponse = await verifyWithBackend(idToken);
    
    // Store tokens
    const authData = await storeAuthData(authResponse);
    
    console.log('Authentication successful');
    
    // Broadcast auth state change
    broadcastAuthState('authenticated', authData);
    
    return authData;

  } catch (error) {
    console.error('Authentication failed:', error);
    
    // Store failed state
    await chrome.storage.local.set({ 
      authData: { 
        isAuthenticated: false, 
        error: error.message 
      } 
    });
    
    // Broadcast auth failure
    broadcastAuthState('failed', { error: error.message });
    
    throw error;
  }
}

// Refresh access token
async function refreshToken() {
  try {
    const result = await chrome.storage.local.get(['authData']);
    const authData = result.authData;

    if (!authData || !authData.refreshToken) {
      throw new Error('No refresh token available');
    }

    console.log('Attempting to refresh token...');
    console.log('Current token expires at:', new Date(authData.expiresAt));
    console.log('Current time:', new Date());

    const response = await fetch(`${AUTH_CONFIG.apiBaseUrl}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh_token: authData.refreshToken })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Refresh token request failed:', response.status, errorText);

      if (response.status === 401) {
        console.log('Refresh token invalid, need to re-authenticate');
        await logout();
        throw new Error('Refresh token expired');
      } else {
        throw new Error(`Refresh failed with status ${response.status}: ${errorText}`);
      }
    }

    const refreshResponse = await response.json();
    console.log('Refresh response received:', refreshResponse);

    // Update stored tokens - keep the same refresh token
    const updatedAuthData = {
      ...authData,
      accessToken: refreshResponse.access_token,
      refreshToken: refreshResponse.refresh_token || authData.refreshToken, // Use new refresh token if provided
      expiresAt: Date.now() + (refreshResponse.expires_in * 1000),
      isAuthenticated: true
    };

    await chrome.storage.local.set({ authData: updatedAuthData });

    console.log('Token refreshed successfully');
    console.log('New token expires at:', new Date(updatedAuthData.expiresAt));

    // Broadcast token refresh
    broadcastAuthState('refreshed', updatedAuthData);

    return updatedAuthData;

  } catch (error) {
    console.error('Token refresh failed:', error);
    await logout();
    throw error;
  }
}

// Check if current token is valid
async function isAuthenticated() {
  const result = await chrome.storage.local.get(['authData']);
  const authData = result.authData;

  if (!authData || !authData.isAuthenticated) {
    console.log('No auth data or not authenticated');
    return false;
  }

  if (!authData.accessToken || !authData.refreshToken) {
    console.log('Missing access token or refresh token');
    return false;
  }

  // Check if token is expired (with 5 minute buffer)
  const now = Date.now();
  const expiresAt = authData.expiresAt || 0;
  const isExpired = now > (expiresAt - 5 * 60 * 1000);

  console.log('Token check:', {
    now: new Date(now),
    expiresAt: new Date(expiresAt),
    isExpired: isExpired,
    timeUntilExpiry: Math.round((expiresAt - now) / 1000 / 60) + ' minutes'
  });

  if (isExpired) {
    console.log('Access token expired, attempting refresh...');
    try {
      await refreshToken();
      return true;
    } catch (error) {
      console.error('Token refresh failed during auth check:', error);
      return false;
    }
  }

  return true;
}

// Logout function
async function logout() {
  await chrome.storage.local.set({ 
    authData: { 
      isAuthenticated: false 
    } 
  });
  
  console.log('Logged out successfully');
  
  // Broadcast logout
  broadcastAuthState('logged_out', { isAuthenticated: false });
}

// Broadcast auth state changes to all extension contexts
function broadcastAuthState(action, data) {
  // Send message to popup and other contexts
  chrome.runtime.sendMessage({
    type: 'AUTH_STATE_CHANGE',
    action: action,
    data: data
  }).catch(() => {
    // Ignore errors if no listeners
  });
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Service worker received message:', message);
  
  if (message.type === 'AUTH_REQUEST') {
    if (message.action === 'login') {
      authenticate()
        .then(authData => sendResponse({ success: true, data: authData }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true; // Keep message channel open for async response
    }
    
    if (message.action === 'logout') {
      logout()
        .then(() => sendResponse({ success: true }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;
    }
    
    if (message.action === 'check_status') {
      isAuthenticated()
        .then(authenticated => sendResponse({ success: true, authenticated }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;
    }
    
    if (message.action === 'refresh') {
      refreshToken()
        .then(authData => sendResponse({ success: true, data: authData }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;
    }
  }
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension started, checking auth status...');
  isAuthenticated().then(authenticated => {
    console.log('Auth status on startup:', authenticated);
  });
});

// Handle extension installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed/updated');
});
