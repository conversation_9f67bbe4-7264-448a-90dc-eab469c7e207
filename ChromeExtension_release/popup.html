<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Sirev Proposal Generator</title>
    <link rel="stylesheet" href="popup.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
  </head>
  <body>
    <header class="px-12 py-10">
      <h1 class="title">Sirev Proposal Generator</h1>
      <div class="subtitle">Selected rooms from <strong>sirev.com</strong> — toggle via the checkbox next to <strong>Grtot</strong></div>
    </header>

    <main id="app" class="px-12 pb-12">
      <!-- Auth section -->
      <section id="auth" class="auth" hidden>
        <div class="auth-content">
          <div class="auth-icon">🔐</div>
          <div class="auth-title">Authentication Required</div>
          <div class="auth-text">
            Please sign in with your Google account to use the Sirev Proposal Generator.
          </div>
          <div class="auth-actions">
            <button id="btnLogin" type="button" class="btn primary">Sign in with Google</button>
          </div>
          <div id="authError" class="auth-error" hidden></div>
        </div>
      </section>

      <!-- User info section -->
      <section id="userInfo" class="user-info" hidden>
        <div class="user-content">
          <div class="user-avatar">
            <img id="userAvatar" src="" alt="User avatar" />
          </div>
          <div class="user-details">
            <div class="user-name" id="userName"></div>
            <div class="user-email" id="userEmail"></div>
          </div>
          <div class="user-actions">
            <button id="btnLogout" type="button" class="btn secondary small">Logout</button>
          </div>
        </div>
      </section>

      <!-- Empty state -->
      <section id="empty" class="empty" hidden>
        <div class="empty-icon">🗒️</div>
        <div class="empty-title">No selections yet</div>
        <div class="empty-text">
          Go to <strong>sirev.com</strong>, and check the box next to a room’s <strong>Grtot</strong> value.
        </div>
      </section>

      <!-- List -->
      <section id="list" class="list" hidden>
        <div class="list-header">
          <div class="count"><span id="count">0</span> selected</div>
          <div class="actions">
            <button id="btnClear" type="button" class="btn danger">Clear all</button>
            <button id="btnGenerate" type="button" class="btn primary">Generate new shareable link</button>
          </div>
        </div>
        <div class="table-container">
          <table id="tripsTable" class="trips-table">
            <thead>
              <tr>
                <th>Room Desc</th>
                <th>Hotel Name</th>
                <th>Location</th>
                <th>Nights</th>
                <th>Tur Operator</th>
                <th>Out Date</th>
                <th>Out Time</th>
                <th>Out Flight</th>
                <th>Out Arr</th>
                <th>Ret Date</th>
                <th>Ret Time</th>
                <th>Ret Flight</th>
                <th>Ret Arr</th>
                <th>Price</th>
                <th>People</th>
                <th>Full Price</th>
                <th>Comment</th>
                <th>Video Link</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="items">
              <!-- Trip rows will be inserted here -->
            </tbody>
          </table>
        </div>
      </section>

      <!-- Technical Info -->
      <section id="tech" class="tech">
        <div class="tech-title">Technical info</div>
        <div class="kv">
          <div class="k">Extension</div>
          <div class="v"><span id="extName">Sirev Proposal Generator</span> <span id="extVer">0.3.0</span></div>
        </div>
        <div class="kv">
          <div class="k">Storage key</div>
          <div class="v"><code id="storageKey">lscSelectedRooms</code></div>
        </div>
        <div class="kv">
          <div class="k">Selected count</div>
          <div class="v" id="techCount">0</div>
        </div>
        <div class="kv">
          <div class="k">Bytes in use</div>
          <div class="v" id="bytesInUse">—</div>
        </div>
        <div class="kv">
          <div class="k">Loaded at</div>
          <div class="v" id="loadedAt">—</div>
        </div>
        <div class="kv" id="lastErrorSection" style="display: none;">
          <div class="k">Last Error</div>
          <div class="v">
            <div id="lastErrorTime" style="font-size: 11px; color: #666;"></div>
            <div id="lastErrorMessage" style="font-size: 12px; color: #d32f2f; word-break: break-word;"></div>
          </div>
        </div>
      </section>
    </main>

    <script src="auth.js"></script>
    <script src="popup.js"></script>
  </body>
</html>
