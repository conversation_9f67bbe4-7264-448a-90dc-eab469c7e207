/* ===== Popup (browser action) — Sirev Proposal Generator =====
 * Shows selected rooms and logs key actions/errors to console.
 * Now also listens to storage changes so selections appear immediately.
 */

(() => {
  const EXT_NAME = "Sirev Proposal Generator";
  const EXT_VER = "0.3.0";
  const STORAGE_KEY = "lscSelectedRooms";

  const LOG_PREFIX = `[${EXT_NAME} Popup]`;
  const log = (...a) => console.log(LOG_PREFIX, ...a);
  const info = (...a) => console.info(LOG_PREFIX, ...a);
  const warn = (...a) => console.warn(LOG_PREFIX, ...a);
  const error = (...a) => console.error(LOG_PREFIX, ...a);

  // Global error capture
  window.addEventListener("error", (e) => error("Uncaught:", e.message, e.filename, e.lineno, e.colno, e.error || ""));
  window.addEventListener("unhandledrejection", (e) => error("Unhandled rejection:", e.reason));

  // Auth manager instance
  const authManager = new AuthManager();

  // UI refs
  const $auth = document.getElementById("auth");
  const $userInfo = document.getElementById("userInfo");
  const $empty = document.getElementById("empty");
  const $list = document.getElementById("list");
  const $items = document.getElementById("items");
  const $count = document.getElementById("count");
  const $techCount = document.getElementById("techCount");
  const $bytesInUse = document.getElementById("bytesInUse");
  const $loadedAt = document.getElementById("loadedAt");
  const $extName = document.getElementById("extName");
  const $extVer = document.getElementById("extVer");
  const $storageKey = document.getElementById("storageKey");
  const $btnRefresh = document.getElementById("btnRefresh");
  const $btnClear = document.getElementById("btnClear");
  const $btnGenerate = document.getElementById("btnGenerate");
  const $btnLogin = document.getElementById("btnLogin");
  const $btnLogout = document.getElementById("btnLogout");
  const $authError = document.getElementById("authError");
  const $userName = document.getElementById("userName");
  const $userEmail = document.getElementById("userEmail");
  const $userAvatar = document.getElementById("userAvatar");

  $extName.textContent = EXT_NAME;
  $extVer.textContent = EXT_VER;
  $storageKey.textContent = STORAGE_KEY;

  // Auth functions - Always read from storage
  async function checkAuthStatus() {
    try {
      // Read auth state directly from storage
      const result = await chrome.storage.local.get(['authData']);
      const authData = result.authData;

      if (authData && authData.isAuthenticated) {
        await showAuthenticatedState();
      } else {
        showUnauthenticatedState();
      }
    } catch (error) {
      error('Auth status check failed:', error);
      showUnauthenticatedState();
    }
  }

  function showUnauthenticatedState() {
    $auth.hidden = false;
    $userInfo.hidden = true;
    $empty.hidden = true;
    $list.hidden = true;
    hideAuthError();
  }

  async function showAuthenticatedState() {
    try {
      // Read user data from storage
      const result = await chrome.storage.local.get(['authData']);
      const authData = result.authData;

      if (authData && authData.user) {
        const user = authData.user;
        $userName.textContent = user.name || 'Unknown User';
        $userEmail.textContent = user.email || '';
        if (user.avatar_url) {
          $userAvatar.src = user.avatar_url;
          $userAvatar.style.display = 'block';
        } else {
          $userAvatar.style.display = 'none';
        }
      }

      $auth.hidden = true;
      $userInfo.hidden = false;
      hideAuthError();

      // Load and display trips
      await loadAndRender();
    } catch (error) {
      error('Failed to show authenticated state:', error);
      showUnauthenticatedState();
    }
  }

  function showAuthError(message) {
    $authError.textContent = message;
    $authError.hidden = false;
  }

  function hideAuthError() {
    $authError.hidden = true;
  }

  async function handleLogin() {
    try {
      $btnLogin.disabled = true;
      $btnLogin.textContent = 'Signing in...';
      hideAuthError();

      // Request authentication from service worker
      await authManager.authenticate();

      // The service worker will broadcast auth state change
      // and storage listener will update UI automatically

    } catch (error) {
      error('Login failed:', error);
      showAuthError(error.message || 'Authentication failed. Please try again.');
      $btnLogin.disabled = false;
      $btnLogin.textContent = 'Sign in with Google';
    }
  }

  async function handleLogout() {
    try {
      await authManager.logout();
      showUnauthenticatedState();
    } catch (error) {
      error('Logout failed:', error);
    }
  }

  function showCopyButton(shareUrl) {
    // Check if copy button already exists
    let copyButton = document.getElementById('btnCopyLink');
    if (!copyButton) {
      // Create copy button
      copyButton = document.createElement('button');
      copyButton.id = 'btnCopyLink';
      copyButton.type = 'button';
      copyButton.className = 'btn secondary';
      copyButton.textContent = 'Copy Share Link';

      // Insert after the generate button
      $btnGenerate.parentNode.insertBefore(copyButton, $btnGenerate.nextSibling);

      // Add event listener
      copyButton.addEventListener('click', async () => {
        try {
          await navigator.clipboard.writeText(shareUrl);
          const originalText = copyButton.textContent;
          copyButton.textContent = 'Copied!';
          copyButton.style.backgroundColor = '#10b981';

          setTimeout(() => {
            copyButton.textContent = originalText;
            copyButton.style.backgroundColor = '';
          }, 1500);
        } catch (error) {
          error('Failed to copy link:', error);
        }
      });
    }

    copyButton.style.display = 'inline-block';
  }

  function hideCopyButton() {
    const copyButton = document.getElementById('btnCopyLink');
    if (copyButton) {
      copyButton.style.display = 'none';
    }
  }

  // Error tracking functions
  function displayLastError(errorMessage) {
    const errorSection = document.getElementById('lastErrorSection');
    const errorTime = document.getElementById('lastErrorTime');
    const errorMsg = document.getElementById('lastErrorMessage');

    if (errorSection && errorTime && errorMsg) {
      errorTime.textContent = new Date().toLocaleString();
      errorMsg.textContent = errorMessage;
      errorSection.style.display = 'block';

      // Store error in storage for persistence
      chrome.storage.local.set({
        lastError: {
          timestamp: Date.now(),
          message: errorMessage
        }
      });
    }
  }

  function clearLastError() {
    const errorSection = document.getElementById('lastErrorSection');
    if (errorSection) {
      errorSection.style.display = 'none';
    }
    chrome.storage.local.remove(['lastError']);
  }

  // Load and display last error on startup
  async function loadLastError() {
    try {
      const result = await chrome.storage.local.get(['lastError']);
      if (result.lastError) {
        const errorSection = document.getElementById('lastErrorSection');
        const errorTime = document.getElementById('lastErrorTime');
        const errorMsg = document.getElementById('lastErrorMessage');

        if (errorSection && errorTime && errorMsg) {
          errorTime.textContent = new Date(result.lastError.timestamp).toLocaleString();
          errorMsg.textContent = result.lastError.message;
          errorSection.style.display = 'block';
        }
      }
    } catch (e) {
      console.error('Failed to load last error:', e);
    }
  }

  function formatBytes(n) {
    if (n == null) return "—";
    if (n < 1024) return `${n} B`;
    if (n < 1024 * 1024) return `${(n / 1024).toFixed(1)} KB`;
    return `${(n / (1024 * 1024)).toFixed(2)} MB`;
  }

  function renderItem(key, data) {
    const row = document.createElement("tr");
    row.dataset.key = key;

    // Create editable cells for each field
    const fields = [
      { key: 'roomDesc', value: data.roomDesc || '', editable: true, isTextarea: true },
      { key: 'name', value: data.name || data.hotelName || '', editable: true },
      { key: 'location', value: data.location || '', editable: true },
      { key: 'nights', value: data.nights || '', editable: true },
      { key: 'tur_operator', value: data.tur_operator || data.flightTo || '', editable: true },
      { key: 'outboundDate', value: data.outboundDate || '', editable: true },
      { key: 'outboundDepTime', value: data.outboundDepTime || '', editable: true },
      { key: 'outboundFlight', value: data.outboundFlight || '', editable: true },
      { key: 'outboundArrTime', value: data.outboundArrTime || '', editable: true },
      { key: 'returnDate', value: data.returnDate || '', editable: true },
      { key: 'returnDepTime', value: data.returnDepTime || '', editable: true },
      { key: 'returnFlight', value: data.returnFlight || '', editable: true },
      { key: 'returnArrTime', value: data.returnArrTime || '', editable: true },
      { key: 'price', value: data.price || data.grtot || '', editable: true },
      { key: 'people_per_trip', value: data.people_per_trip || '', editable: true },
      { key: 'full_price', value: data.full_price || '', editable: true },
      { key: 'comment', value: data.comment || '', editable: true, isTextarea: true },
      { key: 'video_link', value: data.video_link || '', editable: true }
    ];

    fields.forEach(field => {
      const cell = document.createElement("td");

      if (field.editable) {
        const input = document.createElement(field.isTextarea ? "textarea" : "input");
        input.className = field.isTextarea ? "editable comment" : "editable";
        input.value = field.value;
        input.dataset.field = field.key;

        // Auto-save on blur
        input.addEventListener('blur', async () => {
          try {
            const map = await loadMap();
            if (map[key]) {
              map[key][field.key] = input.value;
              await saveMap(map);
              info(`Auto-saved ${field.key} for ${key}`);
            }
          } catch (e) {
            error(`Failed to auto-save ${field.key}:`, e);
          }
        });

        cell.appendChild(input);
      } else {
        cell.textContent = field.value;
      }

      row.appendChild(cell);
    });

    // Actions cell with remove button
    const actionsCell = document.createElement("td");
    const removeBtn = document.createElement("button");
    removeBtn.className = "remove-btn";
    removeBtn.textContent = "Remove";
    removeBtn.addEventListener('click', async () => {
      try {
        const map = await loadMap();
        delete map[key];
        await saveMap(map);
        await render();
        info(`Removed trip ${key}`);
      } catch (e) {
        error(`Failed to remove trip ${key}:`, e);
      }
    });
    actionsCell.appendChild(removeBtn);
    row.appendChild(actionsCell);

    return row;
  }

  function mapToSortedEntries(map) {
    const entries = Object.entries(map || {});
    // Sort by timestamp if available, otherwise preserve insertion order
    // Hotels added later will appear at the end
    entries.sort((a, b) => {
      const A = a[1], B = b[1];
      const timestampA = A.timestamp || 0;
      const timestampB = B.timestamp || 0;

      // If both have timestamps, sort by timestamp (older first)
      if (timestampA && timestampB) {
        return timestampA - timestampB;
      }

      // If only one has timestamp, prioritize the one without (older entries)
      if (timestampA && !timestampB) return 1;
      if (!timestampA && timestampB) return -1;

      // Fallback to key comparison for stable sorting
      return a[0].localeCompare(b[0]);
    });
    return entries;
  }

  function setEmptyState(isEmpty) {
    $empty.hidden = !isEmpty;
    $list.hidden = isEmpty;
  }

  function setCounts(n) {
    $count.textContent = String(n);
    $techCount.textContent = String(n);
  }

  function setLoadedAtNow() {
    const now = new Date();
    $loadedAt.textContent = now.toLocaleString();
  }

  function getBytesForKey(key) {
    return new Promise((resolve) => {
      chrome.storage.local.getBytesInUse([key], (bytes) => {
        resolve(bytes || 0);
      });
    });
  }

  function loadMap() {
    return new Promise((resolve) => {
      chrome.storage.local.get([STORAGE_KEY], (res) => {
        const map = res[STORAGE_KEY] || {};
        info("Loaded selections", { count: Object.keys(map).length, map });
        resolve(map);
      });
    });
  }

  function saveMap(map) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ [STORAGE_KEY]: map }, () => {
        info("Saved selections", { count: Object.keys(map).length });
        resolve();
      });
    });
  }

  async function render() {
    try {
      const map = await loadMap();
      const entries = mapToSortedEntries(map);

      // Counts + bytes
      setCounts(entries.length);
      const bytes = await getBytesForKey(STORAGE_KEY);
      $bytesInUse.textContent = formatBytes(bytes);
      setLoadedAtNow();

      // Empty state
      setEmptyState(entries.length === 0);

      // Render items
      $items.replaceChildren();
      entries.forEach(([key, data]) => {
        const el = renderItem(key, data);
        $items.appendChild(el);
      });

      info("Render complete.", { items: entries.length, bytesInUse: bytes });
    } catch (e) {
      error("Render failed:", e);
    }
  }

  async function clearAll() {
    try {
      await saveMap({});
      await chrome.storage.local.remove(['shareableLink']);

      // Uncheck all checkboxes on the page
      const checkboxes = document.querySelectorAll('input[type="checkbox"]');
      checkboxes.forEach(checkbox => {
        checkbox.checked = false;
      });

      await render();
      hideCopyButton();
      info("Cleared all selections.");
    } catch (e) {
      error("Clear all failed:", e);
    }
  }

  async function generateShareableLink() {
    try {
      $btnGenerate.disabled = true;
      $btnGenerate.textContent = 'Generating...';

      const map = await loadMap();
      const trips = Object.values(map);

      if (trips.length === 0) {
        alert('No trips selected. Please add some trips first.');
        return;
      }

      const response = await authManager.makeAuthenticatedRequest(
        'https://proposal.toptravelagency.ca/hotel-trips:bulk',
        {
          method: 'POST',
          body: JSON.stringify(trips)
        }
      );

      if (!response.ok) {
        // Check if it's an invalid token error
        if (response.status === 401) {
          try {
            // Try to refresh the token
            await authManager.refreshToken();
            // Retry the request
            const retryResponse = await authManager.makeAuthenticatedRequest(
              'https://proposal.toptravelagency.ca/hotel-trips:bulk',
              {
                method: 'POST',
                body: JSON.stringify(trips)
              }
            );

            if (!retryResponse.ok) {
              const retryErrorData = await retryResponse.json();
              const errorMessage = retryErrorData.detail || 'Failed to generate shareable link after retry';
              displayLastError(errorMessage);
              throw new Error(errorMessage);
            }

            const retryResult = await retryResponse.json();

            // Clear any previous errors on success
            clearLastError();

            // Store the shareable link
            await chrome.storage.local.set({ shareableLink: retryResult.share_url });

            // Copy link to clipboard and show success message
            await navigator.clipboard.writeText(retryResult.share_url);

            // Show success message and update UI
            $btnGenerate.textContent = 'Link copied to clipboard!';
            $btnGenerate.style.backgroundColor = '#10b981';

            setTimeout(() => {
              $btnGenerate.textContent = 'Generate new shareable link';
              $btnGenerate.style.backgroundColor = '';
              showCopyButton(retryResult.share_url);
            }, 2000);

            info('Shareable link generated after retry:', retryResult.share_url);
            return;

          } catch (refreshError) {
            // If refresh fails, log out the user
            error('Token refresh failed, logging out:', refreshError);
            await authManager.logout();
            showUnauthenticatedState();
            displayLastError('Authentication expired. Please sign in again.');
            return;
          }
        }

        const errorData = await response.json();
        const errorMessage = errorData.detail || 'Failed to generate shareable link';
        displayLastError(errorMessage);
        throw new Error(errorMessage);
      }

      const result = await response.json();

      // Clear any previous errors on success
      clearLastError();

      // Store the shareable link
      await chrome.storage.local.set({ shareableLink: result.share_url });

      // Copy link to clipboard and show success message
      await navigator.clipboard.writeText(result.share_url);

      // Show success message and update UI
      $btnGenerate.textContent = 'Link copied to clipboard!';
      $btnGenerate.style.backgroundColor = '#10b981';

      setTimeout(() => {
        $btnGenerate.textContent = 'Generate new shareable link';
        $btnGenerate.style.backgroundColor = '';
        // Show the copy button after generating
        showCopyButton(result.share_url);
      }, 2000);

      info('Shareable link generated:', result.share_url);

    } catch (error) {
      error('Failed to generate shareable link:', error);
      displayLastError(error.message);
      alert('Failed to generate shareable link: ' + error.message);
    } finally {
      $btnGenerate.disabled = false;
      if ($btnGenerate.textContent === 'Generating...') {
        $btnGenerate.textContent = 'Generate new shareable link';
      }
    }
  }

  // Wire up controls
  $btnClear.addEventListener("click", () => {
    info("Clear all clicked.");
    clearAll();
  });
  $btnGenerate.addEventListener("click", () => {
    info("Generate new shareable link clicked.");
    generateShareableLink();
  });
  $btnLogin.addEventListener("click", () => {
    info("Login clicked.");
    handleLogin();
  });
  $btnLogout.addEventListener("click", () => {
    info("Logout clicked.");
    handleLogout();
  });

  // Live update when content script saves changes
  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === "local" && changes[STORAGE_KEY]) {
      info("Storage changed; re-rendering.", { changes: changes[STORAGE_KEY] });
      render();
    }
  });

  // Update render function to work with auth
  async function loadAndRender() {
    await render();

    // Check if there's a stored shareable link
    const result = await chrome.storage.local.get(['shareableLink']);
    if (result.shareableLink) {
      showCopyButton(result.shareableLink);
    } else {
      hideCopyButton();
    }
  }

  // Listen for auth state changes from service worker
  window.addEventListener('authStateChange', async (event) => {
    const { action, data } = event.detail;
    console.log('Auth state change in popup:', action, data);

    if (action === 'authenticated' || action === 'refreshed') {
      $btnLogin.disabled = false;
      $btnLogin.textContent = 'Sign in with Google';
      await showAuthenticatedState();
    } else if (action === 'logged_out') {
      showUnauthenticatedState();
    } else if (action === 'failed') {
      $btnLogin.disabled = false;
      $btnLogin.textContent = 'Sign in with Google';
      showAuthError(data.error || 'Authentication failed');
    }
  });

  // Listen for storage changes
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local' && changes.authData) {
      console.log('Auth data changed in storage');
      checkAuthStatus();
    }
  });

  // Initial render
  document.addEventListener("DOMContentLoaded", () => {
    info(`Popup init v${EXT_VER}`);
    checkAuthStatus();
    loadLastError();
  });
})();
