/* ===== Popup styles ===== */
:root {
  --bg: #ffffff;
  --fg: #1f2937;
  --muted: #6b7280;
  --border: #e5e7eb;
  --accent: #111827;
  --danger: #991b1b;
}

* { box-sizing: border-box; }

html, body {
  margin: 0;
  padding: 0;
  background: var(--bg);
  color: var(--fg);
  font: 14px/1.4 system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
}

/* Constrain popup size for consistency - made wider for table */
body { width: 1500px; min-height: 500px; }

.px-12 { padding-left: 12px; padding-right: 12px; }
.py-10 { padding-top: 10px; padding-bottom: 10px; }
.pb-12 { padding-bottom: 12px; }

header {
  border-bottom: 1px solid var(--border);
  background: #fafafa;
}
.title {
  margin: 0 0 2px 0;
  font-size: 16px;
  font-weight: 700;
}
.subtitle {
  margin: 0;
  color: var(--muted);
  font-size: 12px;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.count { font-weight: 600; }

.btn {
  appearance: none;
  border: 1px solid var(--border);
  background: #f9fafb;
  color: var(--accent);
  padding: 4px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 120ms ease, border-color 120ms ease;
  margin-left: 6px;
}
.btn:hover { background: #f3f4f6; border-color: #d1d5db; }
.btn.danger { color: #fff; background: #ef4444; border-color: #ef4444; }
.btn.danger:hover { background: #dc2626; border-color: #dc2626; }

/* Table styles */
.table-container {
  overflow-x: auto;
  border: 1px solid var(--border);
  border-radius: 8px;
  background: white;
}

.trips-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  table-layout: auto;
}

.trips-table th {
  background: #f8f9fa;
  border: 1px solid var(--border);
  padding: 6px 4px;
  text-align: left;
  font-weight: 600;
  font-size: 11px;
  white-space: nowrap;
  position: sticky;
  top: 0;
  z-index: 10;
}

.trips-table td {
  border: 1px solid var(--border);
  padding: 4px;
  vertical-align: top;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Specific column widths */
.trips-table th:nth-child(1), .trips-table td:nth-child(1) { width: 140px; } /* Hotel Name */
.trips-table th:nth-child(2), .trips-table td:nth-child(2) { width: 100px; } /* Location */
.trips-table th:nth-child(3), .trips-table td:nth-child(3) { width: 50px; } /* Nights */
.trips-table th:nth-child(4), .trips-table td:nth-child(4) { width: 60px; } /* Flight To */
.trips-table th:nth-child(5), .trips-table td:nth-child(5) { width: 70px; } /* Out Date */
.trips-table th:nth-child(6), .trips-table td:nth-child(6) { width: 60px; } /* Out Time */
.trips-table th:nth-child(7), .trips-table td:nth-child(7) { width: 70px; } /* Out Flight */
.trips-table th:nth-child(8), .trips-table td:nth-child(8) { width: 60px; } /* Out Arr */
.trips-table th:nth-child(9), .trips-table td:nth-child(9) { width: 70px; } /* Ret Date */
.trips-table th:nth-child(10), .trips-table td:nth-child(10) { width: 60px; } /* Ret Time */
.trips-table th:nth-child(11), .trips-table td:nth-child(11) { width: 70px; } /* Ret Flight */
.trips-table th:nth-child(12), .trips-table td:nth-child(12) { width: 60px; } /* Ret Arr */
.trips-table th:nth-child(13), .trips-table td:nth-child(13) { width: 80px; } /* Grtot */
.trips-table th:nth-child(14), .trips-table td:nth-child(14) { width: 150px; } /* Comment */
.trips-table th:nth-child(15), .trips-table td:nth-child(15) { width: 80px; } /* Actions */

.trips-table tr:nth-child(even) {
  background: #fafafa;
}

.trips-table tr:hover {
  background: #f0f9ff;
}

/* Editable cells */
.editable {
  background: transparent;
  border: none;
  width: 100%;
  padding: 2px;
  font-size: 11px;
  font-family: inherit;
  resize: none;
  min-height: 18px;
  box-sizing: border-box;
}

.editable:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  background: white;
  white-space: normal;
}

.editable.comment {
  min-height: 40px;
  white-space: normal;
  word-wrap: break-word;
}

/* Action buttons */
.remove-btn {
  background: #ef4444;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.remove-btn:hover {
  background: #dc2626;
}

/* Legacy card styles - kept for compatibility but not used in table layout */

.empty {
  text-align: center;
  padding: 24px 6px 16px 6px;
  color: var(--muted);
  border: 1px dashed var(--border);
  border-radius: 10px;
  background: #fafafa;
}
.empty-icon { font-size: 28px; margin-bottom: 6px; }
.empty-title { font-weight: 700; margin-bottom: 4px; }
.empty-text { font-size: 12px; }

.tech {
  margin-top: 12px;
  border-top: 1px solid var(--border);
  padding-top: 10px;
}
.tech-title {
  font-weight: 700;
  margin-bottom: 6px;
}
.kv {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 6px;
  padding: 2px 0;
}
.kv .k { color: var(--muted); font-size: 12px; }
.kv .v { font-size: 12px; }
code { background: #f3f4f6; padding: 1px 4px; border-radius: 4px; }

/* Auth styles */
.auth {
  text-align: center;
  padding: 40px 20px;
}

.auth-content {
  max-width: 300px;
  margin: 0 17%;
}

.auth-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.auth-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--fg);
}

.auth-text {
  color: var(--muted);
  margin-bottom: 24px;
  line-height: 1.5;
}

.auth-actions {
  margin-bottom: 16px;
}

.auth-error {
  color: var(--danger);
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  margin-top: 12px;
}

/* User info styles */
.user-info {
  background: #f9fafb;
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.user-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid var(--border);
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
}

.user-email {
  color: var(--muted);
  font-size: 12px;
}

.user-actions {
  flex-shrink: 0;
}

/* Button variants */
.btn.primary {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn.primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
}

.btn.primary:disabled {
  background: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
}

.btn.secondary {
  background: #f3f4f6;
  color: var(--fg);
  border-color: var(--border);
}

.btn.secondary:hover {
  background: #e5e7eb;
}

.btn.small {
  padding: 4px 8px;
  font-size: 12px;
}

#btnCopyLink {
  margin-left: 8px;
  display: none;
}