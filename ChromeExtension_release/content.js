/* ===== Sirev Proposal Generator — Content Script (sirev.com) =====
 * - Injects a nice checkbox next to each Grtot value.
 * - Toggles selected state and persists to chrome.storage.local.
 * - Logs key actions and all errors to the console.
 */

(() => {
  const EXT_NAME = "Sirev Proposal Generator";
  const EXT_VER = "0.3.0";
  const DEBUG = true; // always log for easier testing

  const LOG_PREFIX = `[${EXT_NAME} CS]`;

  const log = (...args) => DEBUG && console.log(LOG_PREFIX, ...args);
  const info = (...args) => DEBUG && console.info(LOG_PREFIX, ...args);
  const warn = (...args) => DEBUG && console.warn(LOG_PREFIX, ...args);
  const error = (...args) => console.error(LOG_PREFIX, ...args);

  // Global error capture
  window.addEventListener("error", (e) => {
    error("Uncaught error:", e.message, e.filename, e.lineno, e.colno, e.error || "");
  });
  window.addEventListener("unhandledrejection", (e) => {
    error("Unhandled promise rejection:", e.reason);
  });

  // Only run on sirev.com
  try {
    if (!/(^|\.)sirev\.com$/i.test(location.hostname)) {
      info("Not on sirev.com; exiting.", { hostname: location.hostname });
      return;
    }
  } catch (e) {
    error("Hostname check failed:", e);
    return;
  }

  const EXT_KEY = "lscSelectedRooms"; // storage key: { [roomKey]: roomData }

  // Simple stable hash to build a compact room key from text
  const hash = (str) => {
    let h = 2166136261 >>> 0; // FNV-1a
    for (let i = 0; i < str.length; i++) {
      h ^= str.charCodeAt(i);
      h = Math.imul(h, 16777619);
    }
    return "h" + (h >>> 0).toString(36);
  };

  const loadSelectedMap = () =>
    new Promise((resolve) => {
      chrome.storage.local.get([EXT_KEY], (res) => {
        const map = res[EXT_KEY] || {};
        log("Loaded selection map", { count: Object.keys(map).length, map });
        resolve(map);
      });
    });

  const saveSelectedMap = (map) =>
    new Promise((resolve) => {
      chrome.storage.local.set({ [EXT_KEY]: map }, () => {
        log("Saved selection map", { count: Object.keys(map).length, map });
        resolve();
      });
    });

  const text = (node) => (node?.textContent || "").trim();

  function findResultTables() {
    try {
      const tables = Array.from(document.querySelectorAll("table.search"));
      const result = tables.filter((tbl) => {
        // Look for tables that have book button cells
        return tbl.querySelector("td.res-cell-content input[type='button']");
      });
      info("Found result tables with book buttons", { totalTables: tables.length, matched: result.length });
      return result;
    } catch (e) {
      error("findResultTables failed:", e);
      return [];
    }
  }



  function nearestHotelRoot(row) {
    try {
      const root = row.closest('table[id^="hotel-"]');
      return root || null;
    } catch {
      return null;
    }
  }

  function findGrtotCell(row) {
    try {
      // Look for the Grtot cell - it has background color, contains price, and has an <a> tag with hover functionality
      const cells = Array.from(row.querySelectorAll("td"));
      return cells.find(cell => {
        const style = cell.getAttribute('style') || "";
        const hasBackgroundColor = style.includes('background-color: #E7D7C4');
        const hasAnchorTag = cell.querySelector('a[onmouseover]');
        const text = (cell.textContent || "").trim();
        const hasPrice = text.match(/\$\d+/);

        // Grtot cell has background color, price, and anchor tag with hover (for breakdown)
        return hasBackgroundColor && hasPrice && hasAnchorTag;
      }) || null;
    } catch {
      return null;
    }
  }

    function capitalize(str) {
        if (!str) return "";
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    function getRoomDesc(row) {
        // Room desc cell: best-effort — in sample markup it's 2nd col of the primary row
        const roomDescCell = row.querySelector("td:nth-child(2)");
        const roomDescCellSpan = row.querySelector("td:nth-child(2) span");
        let roomText = "";

        for (let i = 0; i < roomDescCell.childNodes.length; i++) {
            const node = roomDescCell.childNodes[i];
            const nextNode = roomDescCell.childNodes[i + 1];
            if (i > 1 && node.localName === "br" && nextNode.nodeName === "#text") {
                roomText = nextNode.textContent;
                break;
            }
        }

        let roomDesc = capitalize(roomDescCellSpan.textContent.trim()) + ' <br> ' + roomText.trim();

        return roomDesc;
    }

  function buildRoomKey(table, row, grtotCell) {
    try {
      const hotelRoot = nearestHotelRoot(row);
      const hotelIdMatch = hotelRoot?.id?.match(/^hotel-(\d+)/);
      const hotelId = hotelIdMatch ? hotelIdMatch[1] : "unknown";

      // Room desc cell: best-effort — in sample markup it's 2nd col of the primary row
      let roomDesc = getRoomDesc(row);

      // Nights: often 3rd cell of the primary row
      const nightsCell = row.querySelector("td:nth-child(3)");
      const nightsTxt = text(nightsCell) || "";

      const displayed = (grtotCell.querySelector("a")?.textContent || grtotCell.textContent || "")
        .replace(/\s+/g, " ")
        .trim();

      const raw = JSON.stringify({ hotelId, roomDesc, nightsTxt, displayed });
      const key = hash(raw);
      log("Built room key", { key, raw: JSON.parse(raw) });
      return key;
    } catch (e) {
      error("buildRoomKey failed:", e);
      return "h-fallback-" + Math.random().toString(36).slice(2);
    }
  }

  function extractRoomData(table, row, grtotCell) {
    try {
      const hotelRoot = nearestHotelRoot(row);

      // Extract hotel name and location from the hotel header
      const nameNode = hotelRoot?.querySelector('td[style*="font-weight: bold"][style*="font-size: 17px"]');
      let hotelName = "Unknown Hotel";
      let location = "Unknown Location";

      if (nameNode) {
        const hotelText = nameNode.innerHTML || "";
        // Extract hotel name (first text node before <br>)
        const hotelNameMatch = hotelText.match(/^([^<]+)/);
        if (hotelNameMatch) {
          hotelName = hotelNameMatch[1].trim();
        }

        // Extract location from span after <br> (before parentheses)
        const locationMatch = hotelText.match(/<span[^>]*>([^(]+)/);
        if (locationMatch) {
          location = locationMatch[1].trim();
        }
      }

      // Room desc cell: best-effort — in sample markup it's 2nd col of the primary row
      let roomDesc = getRoomDesc(row);

      // Extract number of nights
      const nightsCell = row.querySelector("td:nth-child(3)");
      const nights = nightsCell ? (nightsCell.textContent || "").trim() : "?";

      // Extract flight to destination (VAC, CUN, etc.)
      const flightToCell = row.querySelector("td:nth-child(4)");
      const flightTo = flightToCell ? (flightToCell.textContent || "").trim() : "";

      // Extract outbound flight information from current row
      const cells = Array.from(row.querySelectorAll("td"));
      let outboundDate = "";
      let outboundFlight = "";
      let outboundDepTime = "";
      let outboundArrTime = "";
      let returnDate = "";
      let returnFlight = "";
      let returnDepTime = "";
      let returnArrTime = "";

      // Find outbound flight data in current row
      cells.forEach((cell, index) => {
        const cellText = (cell.textContent || "").trim();
        const cellStyle = cell.getAttribute('style') || "";
        const cellClass = cell.getAttribute('class') || "";

        // Check for outbound date (ligne-point-itin class, width:58px)
        if (cellClass.includes('ligne-point-itin') && cellStyle.includes('width:58px') && cellText.match(/[A-Z]{3}\s+\d+/)) {
          outboundDate = cellText;
        }
        // Check for outbound flight number (ligne-point-itin class, width:57px)
        if (cellClass.includes('ligne-point-itin') && cellStyle.includes('width:57px') && cellText.match(/[A-Z]{2}\d+/)) {
          outboundFlight = cellText.trim();
        }
        // Check for outbound times (ligne-point-itin class, width:37px)
        if (cellClass.includes('ligne-point-itin') && cellStyle.includes('width:37px') && cellText.match(/\d{1,2}:\d{2}/)) {
          if (!outboundDepTime) {
            outboundDepTime = cellText;
          } else if (!outboundArrTime) {
            outboundArrTime = cellText;
          }
        }
      });

      // Look for return flight info in the next row
      const nextRow = row.nextElementSibling;
      if (nextRow) {
        const nextCells = Array.from(nextRow.querySelectorAll("td"));
        nextCells.forEach((cell, index) => {
          const cellText = (cell.textContent || "").trim();
          const cellStyle = cell.getAttribute('style') || "";
          const cellClass = cell.getAttribute('class') || "";

          // Check for return date (ligne-point class, width:58px)
          if (cellClass.includes('ligne-point') && cellStyle.includes('width:58px') && cellText.match(/[A-Z]{3}\s+\d+/)) {
            returnDate = cellText;
          }
          // Check for return flight number (ligne-point class, width:57px)
          if (cellClass.includes('ligne-point') && cellStyle.includes('width:57px') && cellText.match(/[A-Z]{2}\d+/)) {
            returnFlight = cellText.trim();
          }
          // Check for return times (ligne-point class, width:37px)
          if (cellClass.includes('ligne-point') && cellStyle.includes('width:37px') && cellText.match(/\d{1,2}:\d{2}/)) {
            if (!returnDepTime) {
              returnDepTime = cellText;
            } else if (!returnArrTime) {
              returnArrTime = cellText;
            }
          }
        });
      }

      // Extract Grtot price
      let grtotValue = (grtotCell.querySelector("a")?.textContent || grtotCell.textContent || "").trim();

      // Remove dollar sign and convert to number
      if (grtotValue.startsWith('$')) {
          grtotValue = grtotValue.slice(1).trim();
      }

      // Convert price string to number (remove commas and parse as float)
      const priceNumber = parseFloat(grtotValue.replace(/,/g, '')) || null;

      // Extract people_per_trip and full_price
      let peoplePerTrip = null;
      let fullPrice = null;

      try {
        // Look for the line after the grtot column that contains "Adults (2x)" pattern
        const peopleCellText = grtotCell.querySelector("div")?.textContent;

        if (peopleCellText) {
          // Look for pattern like "Adults (2x) : $1225 + $884 = $2109"
          const peopleMatch = peopleCellText.match(/([^:]+)\s*:/);
          if (peopleMatch) {
            peoplePerTrip = peopleMatch[1].trim();
          }
        }

        // Look for total price in span tag or as the only price in the total column
        const grtotCellIndex = Array.from(row.cells).indexOf(grtotCell);
        let priceMatch = row.cells[grtotCellIndex - 1].querySelector('span')?.innerText;
        if (priceMatch) {
            priceMatch = priceMatch.match(/\$(\d+(?:,\d{3})*(?:\.\d{2})?)/);
            fullPrice = parseFloat(priceMatch[1]) * 2; // Hardcode 2 peoples
        }

      } catch (e) {
        warn("Failed to extract people_per_trip or full_price:", e);
      }

      const data = {
        name: hotelName,
        location,
        roomDesc,
        nights,
        tur_operator: flightTo,
        outboundDate,
        outboundDepTime,
        outboundFlight,
        outboundArrTime,
        returnDate,
        returnDepTime,
        returnFlight,
        returnArrTime,
        price: priceNumber,
        comment: "", // Initialize empty comment field
        people_per_trip: peoplePerTrip,
        full_price: fullPrice,
        timestamp: Date.now(), // Add timestamp for sorting
        url: location.href
      };
      log("Extracted room data", data);
      return data;
    } catch (e) {
      error("extractRoomData failed:", e);
      return {
        name: "Unknown Hotel",
        location: "Unknown Location",
        roomDesc: "Unknown Room",
        nights: "?",
        tur_operator: "",
        outboundDate: "",
        outboundDepTime: "",
        outboundFlight: "",
        outboundArrTime: "",
        returnDate: "",
        returnDepTime: "",
        returnFlight: "",
        returnArrTime: "",
        price: "?",
        comment: "",
        timestamp: Date.now(),
        url: location.href
      };
    }
  }

  function ensureCheckbox(selectedMap, table, row, bookCell) {
    try {
      if (!bookCell) return;
      if (bookCell.dataset.lscEnhanced === "1") {
        log("Control already injected; skipping this cell.");
        return;
      }

      // Find the Grtot cell for data extraction
      const grtotCell = findGrtotCell(row);
      if (!grtotCell) {
        warn("Could not find Grtot cell for data extraction");
        return;
      }

      const key = buildRoomKey(table, row, grtotCell);
      const data = extractRoomData(table, row, grtotCell);
      const isSelected = Boolean(selectedMap[key]);

      // Create a line break and checkbox
      const br = document.createElement("br");
      bookCell.appendChild(br);

      // Build checkbox + label and append to book cell
      const label = document.createElement("label");
      label.className = "lsc-checkbox";

      const cb = document.createElement("input");
      cb.type = "checkbox";
      cb.checked = isSelected;

      const lbl = document.createElement("span");
      lbl.className = "label";
      lbl.textContent = "Include";

      label.appendChild(cb);
      label.appendChild(lbl);
      bookCell.appendChild(label);

      cb.addEventListener("change", async (e) => {
        try {
          const checked = e.target.checked;
          log("Checkbox toggled", { key, checked });
          const map = await loadSelectedMap();
          if (checked) {
            map[key] = data;
            info("Room selected", { key, data });
          } else {
            delete map[key];
            info("Room unselected", { key });
          }
          await saveSelectedMap(map);
        } catch (err) {
          error("Checkbox change handler failed:", err);
        }
      });

      bookCell.dataset.lscEnhanced = "1";
      log("Injected checkbox into Book cell.");
    } catch (e) {
      error("ensureCheckbox failed:", e);
    }
  }

  async function enhanceAll() {
    console.groupCollapsed(`${LOG_PREFIX} enhanceAll`);
    console.time(`${LOG_PREFIX} enhanceAll time`);
    try {
      const selectedMap = await loadSelectedMap();
      const tables = findResultTables();

      tables.forEach((table, tIndex) => {
        try {
          // Find all book button cells directly by class
          const bookCells = Array.from(table.querySelectorAll("td.res-cell-content"));
          log("Processing book cells", { tableIndex: tIndex, bookCellCount: bookCells.length });

          bookCells.forEach((bookCell, cIndex) => {
            try {
              // Verify this cell has action buttons
              const hasButtons = bookCell.querySelector("input[type='button']");
              if (!hasButtons) return;

              // Get the row this cell belongs to
              const row = bookCell.closest("tr");
              if (!row) return;

              ensureCheckbox(selectedMap, table, row, bookCell);
            } catch (cellErr) {
              error("Book cell processing failed:", { tableIndex: tIndex, cellIndex: cIndex, bookCell }, cellErr);
            }
          });
        } catch (tblErr) {
          error("Table processing failed:", { tableIndex: tIndex, table }, tblErr);
        }
      });

      info("Enhancement pass complete.");
    } catch (e) {
      error("enhanceAll failed:", e);
    } finally {
      console.timeEnd(`${LOG_PREFIX} enhanceAll time`);
      console.groupEnd();
    }
  }

  function startObserver() {
    try {
      const observer = new MutationObserver((mutations) => {
        const added = mutations.reduce((acc, m) => acc + (m.addedNodes?.length || 0), 0);
        if (added > 0) {
          log("MutationObserver detected new nodes; re-enhancing.", { addedNodes: added, mutationCount: mutations.length });
          clearTimeout(startObserver._debounce);
          startObserver._debounce = setTimeout(() => {
            enhanceAll();
          }, 150);
        }
      });

      observer.observe(document.documentElement, { childList: true, subtree: true });
      info("MutationObserver started.");
      return observer;
    } catch (e) {
      error("startObserver failed:", e);
      return null;
    }
  }

  function injectInlineStyleFallback() {
    try {
      if (document.querySelector('link[href$="styles.css"]')) {
        log("External CSS present; inline fallback not needed.");
        return;
      }
      const s = document.createElement("style");
      s.textContent = `
        .lsc-checkbox{display:inline-flex;align-items:center;gap:6px;margin-left:6px;vertical-align:middle;cursor:pointer;user-select:none}
        .lsc-checkbox input[type="checkbox"]{appearance:none;width:16px;height:16px;border:1px solid #8a8a8a;border-radius:4px;background:#fff;position:relative;transition:background .12s,border-color .12s}
        .lsc-checkbox input[type="checkbox"]:hover{border-color:#6b7280}
        .lsc-checkbox input[type="checkbox"]:checked{background:#16a34a;border-color:#0f8a3d}
        .lsc-checkbox input[type="checkbox"]:checked::after{content:"";position:absolute;left:3px;top:0px;width:6px;height:10px;border:2px solid #fff;border-top:0;border-left:0;transform:rotate(45deg)}
        .lsc-checkbox .label{font-size:12px;color:#333}
        .lsc-inline-wrap{white-space:nowrap}
      `;
      document.head.appendChild(s);
      info("Injected inline CSS fallback.");
    } catch (e) {
      error("injectInlineStyleFallback failed:", e);
    }
  }

  // Debug helpers
  window.lscDump = async () => {
    const map = await loadSelectedMap();
    console.log(LOG_PREFIX, "DUMP selected map →", map);
    return map;
  };
  window.lscClear = async () => {
    await saveSelectedMap({});
    console.log(LOG_PREFIX, "CLEARED selection map.");
  };

  // Boot
  (async function init() {
    info(`Init v${EXT_VER}`, { url: location.href });
    injectInlineStyleFallback();
    await enhanceAll();
    startObserver();
    info("Ready.");
  })();
})();
