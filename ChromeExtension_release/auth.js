// Auth module for Chrome extension - Service Worker Communication
class AuthManager {
  constructor() {
    this.apiBaseUrl = 'https://proposal.toptravelagency.ca';

    // Listen for auth state changes from service worker
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'AUTH_STATE_CHANGE') {
        console.log('Auth state change received:', message.action, message.data);
        // Trigger custom event for UI updates
        window.dispatchEvent(new CustomEvent('authStateChange', {
          detail: { action: message.action, data: message.data }
        }));
      }
    });
  }

  // These methods are now handled by the service worker
  // Keeping minimal interface for compatibility

  // Launch Google OAuth flow via service worker
  async authenticate() {
    try {
      console.log('Requesting authentication from service worker...');

      const response = await chrome.runtime.sendMessage({
        type: 'AUTH_REQUEST',
        action: 'login'
      });

      if (!response.success) {
        throw new Error(response.error || 'Authentication failed');
      }

      console.log('Authentication successful via service worker');
      return response.data;

    } catch (error) {
      console.error('Authentication failed:', error);
      throw error;
    }
  }

  // These methods are now handled by the service worker

  // Get stored tokens
  async getStoredTokens() {
    const result = await chrome.storage.local.get(['authData']);
    return result.authData || null;
  }

  // Check if current token is valid via service worker
  async isAuthenticated() {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'AUTH_REQUEST',
        action: 'check_status'
      });

      return response.success ? response.authenticated : false;
    } catch (error) {
      console.error('Auth status check failed:', error);
      return false;
    }
  }

  // Token refresh is now handled by the service worker

  // Get current access token
  async getAccessToken() {
    const authData = await this.getStoredTokens();
    return authData ? authData.accessToken : null;
  }

  // Get current user
  async getCurrentUser() {
    const authData = await this.getStoredTokens();
    return authData ? authData.user : null;
  }

  // Clear stored tokens
  async clearTokens() {
    await chrome.storage.local.remove(['authData']);
  }

  // Refresh token via service worker
  async refreshToken() {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'AUTH_REQUEST',
        action: 'refresh'
      });

      if (!response.success) {
        throw new Error(response.error || 'Token refresh failed');
      }

      console.log('Token refreshed successfully');
      return response.data;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  }

  // Logout via service worker
  async logout() {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'AUTH_REQUEST',
        action: 'logout'
      });

      if (!response.success) {
        throw new Error(response.error || 'Logout failed');
      }

      console.log('Logged out successfully');
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  }

  // Make authenticated API request
  async makeAuthenticatedRequest(url, options = {}) {
    const isAuth = await this.isAuthenticated();
    if (!isAuth) {
      throw new Error('Not authenticated');
    }

    // Get fresh access token from storage
    const authData = await this.getStoredTokens();
    if (!authData || !authData.accessToken) {
      throw new Error('No access token available');
    }

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authData.accessToken}`,
      ...options.headers
    };

    return fetch(url, {
      ...options,
      headers
    });
  }
}

// Export for use in other files
window.AuthManager = AuthManager;
