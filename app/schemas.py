from __future__ import annotations
from pydantic import BaseModel, Field, EmailStr, AnyUrl
from typing import Optional, List

# ---- Auth ----
class GoogleVerifyRequest(BaseModel):
    id_token: str

class UserOut(BaseModel):
    id: str
    email: EmailStr
    google_sub: Optional[str] = None
    name: Optional[str] = None
    avatar_url: Optional[AnyUrl] = None
    is_allowed: bool

class AuthSuccess(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "Bearer"
    expires_in: int
    user: UserOut

class RefreshRequest(BaseModel):
    refresh_token: str

class RefreshResponse(BaseModel):
    access_token: str
    token_type: str = "Bearer"
    expires_in: int

# ---- New submit contract ----
class TripSubmit(BaseModel):
    name: str
    location: str
    nights: Optional[int] = None
    tur_operator: Optional[str] = None  # spelled as provided
    outboundDate: Optional[str] = None
    outboundDepTime: Optional[str] = None
    outboundFlight: Optional[str] = None
    outboundArrTime: Optional[str] = None
    returnDate: Optional[str] = None
    returnDepTime: Optional[str] = None
    returnFlight: Optional[str] = None
    returnArrTime: Optional[str] = None
    price: Optional[float] = None
    roomDesc: Optional[str] = None
    comment: Optional[str] = None
    video_link: Optional[str] = None
    people_per_trip: Optional[str] = None
    full_price: Optional[float] = None

class SubmitTripsResponse(BaseModel):
    share_url: AnyUrl
    link_token: str
    link_id: str
    trips_saved: int
