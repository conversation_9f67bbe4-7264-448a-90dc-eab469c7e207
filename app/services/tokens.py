import os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict
from jose import jwt
from dotenv import load_dotenv

# Ensure .env is loaded
load_dotenv()

def _make_token(sub: str, scope: str, expires_in: int) -> str:
    now = datetime.now(timezone.utc)
    payload: Dict[str, Any] = {
        "iss": os.getenv("JWT_ISSUER", "sirev-proposal"),
        "aud": os.getenv("JWT_AUDIENCE", "sirev-proposal-users"),
        "iat": int(now.timestamp()),
        "nbf": int(now.timestamp()),
        "exp": int((now + timedelta(seconds=expires_in)).timestamp()),
        "sub": sub,
        "scope": scope,
        "type": scope,
    }
    jwt_secret = os.getenv("JWT_SECRET", "dev-secret-change-me-in-production")
    jwt_refresh_secret = os.getenv("JWT_REFRESH_SECRET", "dev-refresh-secret-change-me-in-production")
    secret = jwt_secret if scope == "access" else jwt_refresh_secret
    return jwt.encode(payload, secret, algorithm="HS256")

def issue_tokens(user_id: str):
    access_expires = int(os.getenv("ACCESS_TOKEN_EXPIRES_SECONDS", "2592000"))
    refresh_expires = int(os.getenv("REFRESH_TOKEN_EXPIRES_SECONDS", "2592000"))
    access = _make_token(user_id, "access", access_expires)
    refresh = _make_token(user_id, "refresh", refresh_expires)
    return access, refresh

def verify_token(token: str, scope: str) -> dict:
    jwt_secret = os.getenv("JWT_SECRET", "dev-secret-change-me-in-production")
    jwt_refresh_secret = os.getenv("JWT_REFRESH_SECRET", "dev-refresh-secret-change-me-in-production")
    secret = jwt_secret if scope == "access" else jwt_refresh_secret
    jwt_audience = os.getenv("JWT_AUDIENCE", "sirev-proposal-users")
    jwt_issuer = os.getenv("JWT_ISSUER", "sirev-proposal")
    return jwt.decode(token, secret, algorithms=["HS256"], audience=jwt_audience, issuer=jwt_issuer)

def create_access_token(user_id: str) -> str:
    """Create a new access token for the given user ID"""
    access_expires = int(os.getenv("ACCESS_TOKEN_EXPIRES_SECONDS", "3600"))
    return _make_token(user_id, "access", access_expires)
