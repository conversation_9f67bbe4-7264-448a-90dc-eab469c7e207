from __future__ import annotations
import logging
import httpx
from typing import Optional, Dict, Any, List
import os
from dotenv import load_dotenv

logger = logging.getLogger("tripadvisor")

# Ensure .env is loaded
load_dotenv()

class TripAdvisorClient:
    def __init__(self, api_key: str | None = None, base_url: str | None = None, timeout: int | None = None):
        # Load environment variables with proper defaults
        self.api_key = api_key or os.getenv("TRIPADVISOR_API_KEY", "")
        self.base_url = (base_url or os.getenv("TRIPADVISOR_BASE_URL", "https://api.content.tripadvisor.com")).rstrip("/")
        self.timeout = timeout or int(os.getenv("TRIPADVISOR_TIMEOUT_SECONDS", "8"))

        # Debug logging to verify env vars are loaded
        logger.info(f"TripAdvisor client initialized with base_url: {self.base_url}")
        logger.info(f"API key present: {bool(self.api_key)}")
        if self.api_key:
            logger.info(f"API key (first 8 chars): {self.api_key[:8]}...")
        self._client = httpx.Client(timeout=self.timeout)

    def _get(self, path: str, params: dict) -> httpx.Response:
        url = f"{self.base_url}{path}"
        headers = {
            "accept": "application/json"
        }
        if self.api_key:
            params["key"] = self.api_key

        logger.info("TripAdvisor GET %s params=%s", url, params)
        resp = self._client.get(url, params=params, headers=headers)
        body_preview = resp.text[:2000]
        logger.info("TripAdvisor RESP %s %s %s", resp.status_code, url, body_preview)
        return resp

    def search_hotel(self, name: str, location: str) -> Optional[Dict[str, Any]]:
        """Search hotel by name and location. Returns best location match or first result."""
        # NOTE: Endpoint path & params must be aligned with your actual TripAdvisor Content API.
        # The following is a placeholder structure.
        if not self.api_key:
            logger.warning("TripAdvisor API key not configured, skipping search for %s", name)
            return None

        try:
            resp = self._get("/api/v1/location/search?category=hotels&language=en", {"searchQuery": f"{name}, {location}"})
            if resp.status_code != 200:
                logger.warning("TripAdvisor search returned status %d for %s", resp.status_code, name)
                return None
            data = resp.json()
            logger.info("TripAdvisor search returned %d results for %s", len(data), name)
            items = data.get("data") or data.get("results") or []

            if not items:
                return None

            # If we only have one result, return it
            if len(items) == 1:
                return items[0]

            # Find the best location match
            best_match = None

            for item in items:
                if item.get("name").lower() == name.lower():
                    best_match = item
                    break

            # Get the best match
            selected_hotel = best_match if best_match else items[0]

            # Get detailed information for the selected hotel
            location_id = selected_hotel.get("location_id")
            if location_id:
                details = self.get_hotel_details(location_id)
                if details:
                    # Merge search result with details
                    selected_hotel.update(details)

            return selected_hotel

        except Exception as e:
            logger.exception("TripAdvisor search failed: %s", e)
            return None

    def get_hotel_details(self, location_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed hotel information by location_id"""
        if not self.api_key:
            logger.warning("TripAdvisor API key not configured, skipping details for %s", location_id)
            return None

        try:
            resp = self._get(f"/api/v1/location/{location_id}/details", {})
            if resp.status_code != 200:
                logger.warning("TripAdvisor details returned status %d for location_id %s", resp.status_code, location_id)
                return None

            data = resp.json()

            # Extract the fields we want
            details = {}

            if "description" in data:
                details["description"] = data["description"]

            if "ranking_data" in data:
                ranking_data = data["ranking_data"]
                details["ranking_out_of"] = ranking_data.get("ranking_out_of")
                details["ranking"] = ranking_data.get("ranking")

            if "rating" in data:
                details["rating"] = data["rating"]

            if "num_reviews" in data:
                details["num_reviews"] = data["num_reviews"]

            if "subratings" in data:
                details["subratings"] = data["subratings"]

            if "amenities" in data:
                # Extract amenity names and sort alphabetically
                amenities = []
                for amenity in data["amenities"]:
                    if isinstance(amenity, dict) and "name" in amenity:
                        amenities.append(amenity["name"])
                    elif isinstance(amenity, str):
                        amenities.append(amenity)
                details["amenities"] = sorted(amenities)

            if "web_url" in data:
                details["web_url"] = data["web_url"]

            if "see_all_photos" in data:
                details["see_all_photos"] = data["see_all_photos"]

            return details

        except Exception as e:
            logger.exception("TripAdvisor details fetch failed: %s", e)
            return None

    def get_hotel_images(self, hotel_id: str) -> List[Dict[str, Any]]:
        if not self.api_key:
            logger.warning("TripAdvisor API key not configured, skipping images for hotel %s", hotel_id)
            return []

        try:
            resp = self._get(f"/api/v1/location/{hotel_id}/photos?language=en&source=Expert%2CManagement", {})
            if resp.status_code != 200:
                logger.warning("TripAdvisor images returned status %d for hotel %s", resp.status_code, hotel_id)
                return []
            data = resp.json()
            items = data.get("data") or data.get("images") or []
            return items
        except Exception as e:
            logger.exception("TripAdvisor images failed: %s", e)
            return []

    def get_hotel_reviews(self, hotel_id: str) -> List[Dict[str, Any]]:
        if not self.api_key:
            logger.warning("TripAdvisor API key not configured, skipping reviews for hotel %s", hotel_id)
            return []

        try:
            # resp = self._get(f"/content-api/v1/hotels/{hotel_id}/reviews", {})
            resp = self._get(f"/api/v1/location/{hotel_id}/reviews?language=en&limit=20&offset=0", {})
            if resp.status_code != 200:
                logger.warning("TripAdvisor reviews returned status %d for hotel %s", resp.status_code, hotel_id)
                return []
            data = resp.json()
            items = data.get("data") or data.get("reviews") or []
            return items
        except Exception as e:
            logger.exception("TripAdvisor reviews failed: %s", e)
            return []

    def close(self):
        self._client.close()
