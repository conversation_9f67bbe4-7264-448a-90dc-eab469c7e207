from __future__ import annotations
import asyncio, random
from typing import List, <PERSON>ple
import os

# For MVP we implement a 'mock' provider.
# Returns a few image URLs and dummy amenities/reviews with randomization.
# In production, replace with adapter hitting a legal provider API.
async def enrich_one(hotel_name: str, provider: str = "mock") -> Tuple[list[dict], list[dict], list[dict]]:
    await asyncio.sleep(0.05)  # simulate latency
    if provider != "mock":
        # Placeholder for real integrations
        raise NotImplementedError("Only mock provider is implemented in MVP")
    # Generate deterministic-ish data per hotel
    rnd = abs(hash(hotel_name)) % 1000
    images = [
        {"url": f"https://picsum.photos/seed/{rnd+i}/640/360", "provider": "mock", "width": 640, "height": 360, "attribution": "mock"},
        {"url": f"https://picsum.photos/seed/{rnd+i+5}/800/450", "provider": "mock", "width": 800, "height": 450, "attribution": "mock"},
    ]
    amenities = [{"name": x, "provider": "mock"} for x in ["Pool", "Free WiFi", "Gym", "Spa"]]
    reviews = [
        {"review_id": f"r{rnd}", "reviewer_name": "Traveler A", "rating": 4.3, "text": f"Loved {hotel_name}", "url": None, "provider": "mock"},
        {"review_id": f"s{rnd}", "reviewer_name": "Traveler B", "rating": 4.0, "text": "Great value", "url": None, "provider": "mock"},
    ]
    return images, amenities, reviews

async def enrich_hotels_concurrently(hotels: list[str]) -> dict[str, tuple[list[dict], list[dict], list[dict]]]:
    enrich_concurrency = int(os.getenv("ENRICH_CONCURRENCY", "6"))
    enrich_provider = os.getenv("ENRICH_PROVIDER", "mock")
    enrich_per_call_timeout = int(os.getenv("ENRICH_PER_CALL_TIMEOUT_SECONDS", "5"))
    enrich_global_timeout = int(os.getenv("ENRICH_GLOBAL_TIMEOUT_SECONDS", "25"))

    sem = asyncio.Semaphore(enrich_concurrency)
    results: dict[str, tuple[list[dict], list[dict], list[dict]]] = {}

    async def worker(name: str):
        async with sem:
            try:
                images, amenities, reviews = await asyncio.wait_for(
                    enrich_one(name, enrich_provider),
                    timeout=enrich_per_call_timeout,
                )
                results[name] = (images, amenities, reviews)
            except Exception:
                results[name] = ([], [], [])

    await asyncio.wait_for(asyncio.gather(*(worker(n) for n in hotels)), timeout=enrich_global_timeout)
    return results
