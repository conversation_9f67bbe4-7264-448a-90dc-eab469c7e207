from __future__ import annotations

import time
import os
from typing import List, Tu<PERSON>, Dict

from dotenv import load_dotenv
from flask import request, jsonify
from sqlalchemy.orm import Session
from sqlalchemy import select, delete
from app.db import get_session
from app.models import User, ShareLink, Hotel, Trip, ShareLinkTrip, HotelImage, HotelAmenity, HotelReview
from app.schemas import TripSubmit, SubmitTripsResponse
from app.auth import get_current_user_from_request
from urllib.parse import urljoin
import secrets, logging, re
from datetime import datetime, date

from app.services.tripadvisor import TripAdvisorClient

logger = logging.getLogger("api.hotels")

def gen_token() -> str:
    return secrets.token_urlsafe(18)

def parse_location(location: str) -> Tuple[str | None, str | None]:
    if not location:
        return None, None

    # Remove extra text in parentheses like "(view the guide)"
    location = re.sub(r'\s*\([^)]*\)\s*', '', location).strip()

    parts = [p.strip() for p in location.split(",") if p.strip()]
    if len(parts) == 1:
        return None, clean_html_tags(parts[0])
    return clean_html_tags(parts[0]), clean_html_tags(parts[-1])  # city, country

def clean_html_tags(text: str | None) -> str | None:
    """Remove all HTML tags except <br> (any of <br>, <br/>, <br />) and clean up whitespace."""
    if not text:
        return None

    # Normalize all <br> variants to a canonical "<br>"
    normalized = re.sub(r'<\s*br\s*/?\s*>', '<br>', text, flags=re.IGNORECASE)

    # Remove every other HTML tag (leave <br> intact)
    without_tags = re.sub(r'</?(?!br\b)[^>]+>', '', normalized, flags=re.IGNORECASE)

    # Clean up whitespace (keeps the literal "<br>" tokens)
    cleaned = re.sub(r'\s+', ' ', without_tags).strip()

    return cleaned if cleaned else None

def clean_country(value: str) -> str:
    """
    Removes everything after the first <br> tag
    and trims leading/trailing whitespace.
    """
    if not value:
        return ""

    # Split at <br> and take the first part
    cleaned = value.split("<br>", 1)[0]

    # Trim whitespace, tabs, newlines
    return cleaned.strip()


def clean_room_desc(room_desc: str | None) -> str | None:
    """Extract room description content, handling both span tags and plain text with <br> tags."""
    if not room_desc:
        return None

    # Split by lines first
    lines = room_desc.split('\n')
    if not lines:
        return None

    # Extract span content from first line if it exists
    first_line = lines[0].strip()
    span_content = ""

    # Extract content from span tags
    span_matches = re.findall(r'<span[^>]*>(.*?)</span>', first_line, re.IGNORECASE | re.DOTALL)
    if span_matches:
        span_content = ' '.join(span_matches).strip()
    else:
        # If no span tags, use the entire first line as content
        span_content = first_line

    # Get second line if it exists
    second_line = ""
    if len(lines) > 1:
        second_line = lines[1].strip()

    # Combine span content and second line
    result = f"{span_content} {second_line}".strip()

    return result if result else None

def clean_all_special_spaces(text: str | None) -> str | None:
    """Remove all special Unicode spaces and normalize to regular spaces."""
    if not text:
        return None
    
    # Replace various Unicode space characters with regular space
    cleaned = re.sub(r'[\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000\uFEFF]', ' ', text)
    
    # Normalize multiple spaces to single space
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    
    return cleaned if cleaned else None

def parse_review_date(date_str: str | None) -> datetime | None:
    """Parse review date from ISO format to datetime."""
    if not date_str:
        return None
    try:
        # Parse ISO format and convert to date only
        dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        return dt.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)
    except (ValueError, AttributeError):
        return None

def convert_trip_operator(operator: str | None) -> str | None:
    """Convert tour operator codes to readable names."""
    if not operator:
        return None
    
    operator_map = {
        "CAH":"Caribe Sol",
        "CLM":"Club Med",
        "ETG":"Enjoy Travel Group",
        "HOL":"Holasun",
        "POR":"Porter Escapes",
        "SQV":"Sunquest Vacations",
        "STC":"The Special Travel",
        "SWG":"Sunwing Vacations",
        "TBA":"TravelBrands",
        "VAC":"Air Canada Vacations",
        "VAT":"Transat",
        "VWQ":"Vacances Westjet Quebec",
        "WJS":"WestJet Vacations",
    }
    
    code = operator.upper().strip()
    return operator_map.get(code, operator)

def safe_int(value):
    """Safely convert value to integer"""
    if value is None:
        return None
    try:
        return int(value)
    except (ValueError, TypeError):
        return None

def safe_float(value):
    """Safely convert value to float"""
    if value is None:
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def get_db_session():
    """Helper to get database session for synchronous operations"""
    return get_session()

def submit_trips_bulk(body):
    """Submit selected hotels and create a shareable link"""
    try:
        load_dotenv()
        
        # Parse request body
        if not isinstance(body, list) or len(body) == 0:
            return {"code": "BAD_REQUEST", "message": "Expected a non-empty JSON array."}, 400
        if len(body) > 50:
            return {"code": "BAD_REQUEST", "message": "Too many trips in one request (max 50)."}, 400

        # Get current user (sync version for Flask)
        from flask import request
        from app.services.tokens import verify_token
        from sqlalchemy import select
        from app.models import User

        # Get authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return {"code": "UNAUTHORIZED", "message": "Missing/invalid token"}, 401

        token = auth_header[7:]  # Remove 'Bearer ' prefix

        try:
            payload = verify_token(token, scope="access")
        except Exception:
            return {"code": "UNAUTHORIZED", "message": "Invalid token"}, 401

        user_id = payload.get("sub")
        if not user_id:
            return {"code": "UNAUTHORIZED", "message": "Invalid token"}, 401

        # Convert string user_id to integer
        try:
            user_id_int = int(user_id)
        except (ValueError, TypeError):
            return {"code": "UNAUTHORIZED", "message": "Invalid user ID in token"}, 401

        logger.info("/hotel-trips:bulk received %d trips from user_id=%s", len(body), user_id_int)

        # Get database session for all operations
        db = get_db_session()
        try:
            # Fetch user
            res = db.execute(select(User).where(User.id == user_id_int))
            user = res.scalar_one_or_none()
            if not user or not user.is_allowed:
                return {"code": "UNAUTHORIZED", "message": "User not found or not allowed"}, 401
            # 1) Create share link
            token = gen_token()
            link = ShareLink(user_id=user.id, token=token, title=None, note=None, is_active=True)
            db.add(link)
            db.flush()

            tripadvisor = TripAdvisorClient()

            trips_created: List[Trip] = []
            try:
                for idx, trip_data in enumerate(body):
                    # Convert dict to TripSubmit-like object
                    trip_in = type('TripSubmit', (), trip_data)()
                    
                    city, country = parse_location(getattr(trip_in, 'location', '') or "")
                    country = clean_country(country)

                    # 2) Find or create Hotel by (name, city, country)
                    stmt = select(Hotel).where(Hotel.name == trip_in.name)
                    if city:
                        stmt = stmt.where(Hotel.city == city)
                    if country:
                        stmt = stmt.where(Hotel.country == country)
                    res = db.execute(stmt)
                    hotel = res.scalar_one_or_none()

                    if hotel is None:
                        logger.info("Hotel not found locally. Searching TripAdvisor name=%s location=%s", trip_in.name, getattr(trip_in, 'location', ''))
                        ta_hit = tripadvisor.search_hotel(trip_in.name, getattr(trip_in, 'location', '') or "")
                        if not ta_hit:
                            logger.warning("TripAdvisor search returned no results for %s / %s", trip_in.name, getattr(trip_in, 'location', ''))
                            # Create minimal hotel from provided data
                            hotel = Hotel(name=trip_in.name, source_hotel_id=None, address=None, city=city, country=country)
                        else:
                            # Extract fields from TripAdvisor response - prioritize location_id and address_string
                            source_id = str(ta_hit.get("location_id") or ta_hit.get("id") or ta_hit.get("hotel_id") or "")

                            # Check if hotel already exists by source_hotel_id
                            if source_id:
                                existing_by_source = db.execute(select(Hotel).where(Hotel.source_hotel_id == source_id))
                                hotel = existing_by_source.scalar_one_or_none()
                                if hotel:
                                    logger.info(f"Found existing hotel by source_hotel_id: {source_id}")
                                else:
                                    # Create new hotel with TripAdvisor data
                                    addr = ta_hit.get("address_string") or ta_hit.get("address") or ta_hit.get("address_obj", {}).get("address_string")
                                    cty = ta_hit.get("address_obj", {}).get("city") or city
                                    ctry = ta_hit.get("address_obj", {}).get("country") or country

                                    # Create hotel with TripAdvisor details (with proper type conversion)

                                    hotel = Hotel(
                                        name=trip_in.name,
                                        source_hotel_id=source_id or None,
                                        address=addr,
                                        city=cty,
                                        country=ctry,
                                        description=ta_hit.get("description"),
                                        ranking_out_of=safe_int(ta_hit.get("ranking_out_of")),
                                        ranking=safe_int(ta_hit.get("ranking")),
                                        rating=safe_float(ta_hit.get("rating")),
                                        num_reviews=safe_int(ta_hit.get("num_reviews")),
                                        subratings=ta_hit.get("subratings"),
                                        amenities_data=ta_hit.get("amenities"),
                                        web_url=ta_hit.get("web_url"),
                                        see_all_photos=ta_hit.get("see_all_photos"),
                                        video_link=getattr(trip_in, 'video_link', None) if hasattr(trip_in, 'video_link') else None
                                    )
                                    db.add(hotel)
                                    db.flush()
                            else:
                                # Create hotel without source_hotel_id
                                addr = ta_hit.get("address_string") or ta_hit.get("address") or ta_hit.get("address_obj", {}).get("address_string")
                                cty = ta_hit.get("address_obj", {}).get("city") or city
                                ctry = ta_hit.get("address_obj", {}).get("country") or country
                                hotel = Hotel(name=trip_in.name, address=addr, city=cty, country=ctry)
                                db.add(hotel)
                                db.flush()
                        db.add(hotel)
                        db.flush()

                        # Fetch images & reviews if we have a source id
                        if hotel.source_hotel_id:
                            try:
                                time.sleep(1)  # Rate limit to 2 requests per second
                                images = tripadvisor.get_hotel_images(hotel.source_hotel_id)
                                for im in images:
                                    # Extract image URLs from the nested structure
                                    images_data = im.get("images", {})
                                    original = images_data.get("original", {})
                                    large = images_data.get("large", {})
                                    medium = images_data.get("medium", {})

                                    # Prefer original, then large, then medium
                                    url = original.get("url") or large.get("url") or medium.get("url")
                                    if url:
                                        # Get dimensions from the selected image size
                                        width = original.get("width") or large.get("width") or medium.get("width")
                                        height = original.get("height") or large.get("height") or medium.get("height")

                                        db.add(HotelImage(
                                            hotel_id=hotel.id,
                                            url=url,
                                            width=width,
                                            height=height,
                                            attribution=im.get("caption") or None,
                                            provider="tripadvisor"
                                        ))
                                reviews = tripadvisor.get_hotel_reviews(hotel.source_hotel_id)
                                for rv in reviews[:20]:
                                    review_date = parse_review_date(rv.get("published_date"))
                                    review_text = (rv.get("title", "") + " \n " + rv.get("text", "")).strip()
                                    db.add(HotelReview(
                                        hotel_id=hotel.id,
                                        review_id=str(rv.get("id")),
                                        reviewer_name=rv.get("user.username"),
                                        rating=rv.get("rating"),
                                        text=review_text,
                                        url=rv.get("url"),
                                        provider="tripadvisor",
                                        date=review_date
                                    ))
                            except Exception as e:
                                logger.exception("TripAdvisor enrichment failed for hotel_id=%s: %s", hotel.id, e)
                    else:
                        logger.info("Found existing hotel id=%s for %s", hotel.id, hotel.name)

                    # Update hotel video_link if provided
                    if getattr(trip_in, 'video_link', None) and hotel:
                        hotel.video_link = trip_in.video_link
                        db.add(hotel)

                    # 3) Create Trip
                    trip = Trip(
                        hotel_id=hotel.id,
                        nights=safe_int(getattr(trip_in, 'nights', None)),
                        tour_operator=getattr(trip_in, 'tur_operator', None),  # Store original value
                        outbound_date=clean_all_special_spaces(getattr(trip_in, 'outboundDate', None)),
                        outbound_dep_time=getattr(trip_in, 'outboundDepTime', None),
                        outbound_flight=clean_all_special_spaces(getattr(trip_in, 'outboundFlight', None)),
                        outbound_arr_time=getattr(trip_in, 'outboundArrTime', None),
                        return_date=clean_all_special_spaces(getattr(trip_in, 'returnDate', None)),
                        return_dep_time=getattr(trip_in, 'returnDepTime', None),
                        return_flight=clean_all_special_spaces(getattr(trip_in, 'returnFlight', None)),
                        return_arr_time=getattr(trip_in, 'returnArrTime', None),
                        price=safe_float(getattr(trip_in, 'price', None)),
                        room_desc=clean_html_tags(clean_room_desc(getattr(trip_in, 'roomDesc', None))),
                        comment=getattr(trip_in, 'comment', None),
                        people_per_trip=getattr(trip_in, 'people_per_trip', None),
                        full_price=safe_float(getattr(trip_in, 'full_price', None)),
                    )
                    db.add(trip)
                    db.flush()
                    trips_created.append(trip)

                    # 4) Link trip to share link (ordering preserved)
                    db.add(ShareLinkTrip(share_link_id=link.id, trip_id=trip.id, position=idx))

                db.commit()
            finally:
                tripadvisor.close()

            # 5) Build share URL
            base_url = request.host_url.rstrip("/")
            share_url = f"{base_url}/s/{token}"

            logger.info("Created share link %s with %d trips", token, len(trips_created))
            return {
                "share_url": share_url,
                "link_token": token,
                "link_id": str(link.id),
                "trips_saved": len(trips_created)
            }, 200
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in submit_trips_bulk: {e}")
        env = os.getenv("ENV", "production")
        if env == "dev":
            import traceback
            return {
                "code": "INTERNAL_ERROR",
                "message": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }, 500
        else:
            return {"code": "INTERNAL_ERROR", "message": "Internal server error"}, 500

# Direct function for Connexion (no wrapper needed)
def submit_trips_bulk_sync(body):
    """Direct call to synchronous submit_trips_bulk"""
    return submit_trips_bulk(body)
