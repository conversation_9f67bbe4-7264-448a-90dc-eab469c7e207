from __future__ import annotations
import os
from dotenv import load_dotenv
from flask import request, make_response
from sqlalchemy.orm import Session
from sqlalchemy import select
from app.db import get_session
from app.models import ShareLink, ShareLinkTrip, Trip, Hotel, HotelImage, HotelReview
from jinja2 import Environment, FileSystemLoader, select_autoescape
from app.controllers.hotels import convert_trip_operator
import logging

# Ensure .env is loaded
load_dotenv()

logger = logging.getLogger("api.public")

TEMPLATES_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates")
jinja_env = Environment(
    loader=FileSystemLoader(TEMPLATES_DIR),
    autoescape=select_autoescape(["html", "xml"]),
)

# Add custom filters
jinja_env.filters['convert_tour_operator'] = convert_trip_operator

def get_db_session():
    """Helper to get database session for synchronous operations"""
    return get_session()

def share_page(token):
    """Public share page (HTML)"""
    try:
        # Get database session
        db = get_db_session()
        try:
            res = db.execute(select(ShareLink).where(ShareLink.token == token, ShareLink.is_active == True))
            link = res.scalar_one_or_none()
            if not link:
                return {"code": "NOT_FOUND", "message": "Not found"}, 404

            res = db.execute(
                select(ShareLinkTrip, Trip, Hotel)
                .join(Trip, ShareLinkTrip.trip_id == Trip.id)
                .join(Hotel, Trip.hotel_id == Hotel.id)
                .where(ShareLinkTrip.share_link_id == link.id)
                .order_by(ShareLinkTrip.position.asc())
            )
            rows = res.all()

            blocks = []
            destinations = set()
            travel_dates = []

            for slt, trip, hotel in rows:
                imgs = (db.execute(select(HotelImage).where(HotelImage.hotel_id == hotel.id))).scalars().all()
                revs = (db.execute(select(HotelReview).where(HotelReview.hotel_id == hotel.id))).scalars().all()

                # Collect destination and date info
                if hotel.city:
                    destinations.add(hotel.city)
                if trip.outbound_date and trip.return_date:
                    travel_dates.append(f"{trip.outbound_date} - {trip.return_date}")

                # Structure hotel data with proper image format
                hotel_images = []
                for img in imgs:
                    hotel_images.append({
                        'url': img.url,
                        'thumbnail_url': img.url,  # Use the same URL for thumbnail
                        'alt': img.attribution or f"{hotel.name} image",
                        'width': img.width,
                        'height': img.height
                    })

                # Structure amenities data from the new amenities_data JSON field
                hotel_amenities = []
                if hotel.amenities_data and isinstance(hotel.amenities_data, list):
                    # Use amenities from TripAdvisor (already sorted alphabetically)
                    for amenity_name in hotel.amenities_data:
                        hotel_amenities.append({
                            'name': amenity_name,
                            'category': 'general'  # We don't have categories from TripAdvisor
                        })
                else:
                    # Add sample amenities if none exist (for demo purposes)
                    hotel_amenities = [
                        {'name': 'Air Conditioning', 'category': 'comfort'},
                        {'name': 'Bar/Lounge', 'category': 'dining'},
                        {'name': 'Beach Access', 'category': 'location'},
                        {'name': 'Concierge', 'category': 'service'},
                        {'name': 'Fitness Center', 'category': 'recreation'},
                        {'name': 'Free WiFi', 'category': 'connectivity'},
                        {'name': 'Restaurant', 'category': 'dining'},
                        {'name': 'Room Service', 'category': 'service'},
                        {'name': 'Spa Services', 'category': 'wellness'},
                        {'name': 'Swimming Pool', 'category': 'recreation'}
                    ]
                    # Sort sample amenities alphabetically by name
                    hotel_amenities.sort(key=lambda x: x['name'])

                blocks.append({
                    "hotel": {
                        **hotel.__dict__,
                        "images": hotel_images,
                        "amenities": hotel_amenities,
                        "reviews": revs
                    },
                    "trip": trip
                })

            # Determine destination and travel dates for cover
            destination = ", ".join(destinations) if destinations else "Your Destination"
            travel_dates_str = travel_dates[0] if travel_dates else "Your Travel Dates"

            template = jinja_env.get_template("new_share_clean.html")
            html = template.render(
                # Company information from environment
                company_name=os.getenv("COMPANY_NAME", "Sirev Travel Agency"),
                company_tagline=os.getenv("COMPANY_TAGLINE", "Tailored Travel Proposal"),
                company_address=os.getenv("COMPANY_ADDRESS", "123 Main St, Toronto, ON, Canada"),
                company_phone=os.getenv("COMPANY_PHONE", "****** 123 4567"),
                company_email=os.getenv("COMPANY_EMAIL", "<EMAIL>"),
                company_website=os.getenv("COMPANY_WEBSITE", "https://www.sirev.com"),
                company_facebook=os.getenv("COMPANY_FACEBOOK", "https://facebook.com/sirev"),
                company_instagram=os.getenv("COMPANY_INSTAGRAM", "https://instagram.com/sirev"),

                # Agent information (can be customized per proposal)
                agent_name=os.getenv("DEFAULT_AGENT_NAME", "Travel Specialist"),
                agent_email=os.getenv("DEFAULT_AGENT_EMAIL", "<EMAIL>"),
                agent_phone=os.getenv("DEFAULT_AGENT_PHONE", "****** 123 4567"),

                # Proposal specific data
                destination=destination,
                travel_dates=travel_dates_str,
                client_name="Valued Client",  # Could be added to ShareLink model later

                # Template data
                title=link.title or "Travel Proposal",
                note=link.note,
                token=token,
                blocks=blocks,
                base_url=request.host_url.rstrip("/")
            )
            
            # Return HTML response
            response = make_response(html)
            response.headers['Content-Type'] = 'text/html'
            return response
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in share_page: {e}")
        env = os.getenv("ENV", "production")
        if env == "dev":
            import traceback
            return {
                "code": "INTERNAL_ERROR",
                "message": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }, 500
        else:
            return {"code": "INTERNAL_ERROR", "message": "Internal server error"}, 500

# Direct function for Connexion (no wrapper needed)
def share_page_sync(token):
    """Direct call to synchronous share_page"""
    return share_page(token)
