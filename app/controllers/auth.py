from __future__ import annotations
import os
from dotenv import load_dotenv
from flask import request, jsonify
from sqlalchemy.orm import Session
from sqlalchemy import select
from app.db import get_session
from app.models import User
from app.schemas import GoogleVerifyRequest, AuthSuccess, RefreshRequest, RefreshResponse, UserOut
from app.services.tokens import issue_tokens, verify_token, create_access_token
from app.auth import verify_google_id_token, get_current_user_from_request
from jose import jwt
import logging

# Ensure .env is loaded
load_dotenv()

logger = logging.getLogger("api.auth")

def get_db_session():
    """Helper to get database session for synchronous operations"""
    return get_session()

def google_verify(body):
    """Verify Google ID token and issue application tokens"""
    try:
        # Parse request body
        if isinstance(body, dict):
            id_token = body.get('id_token')
        else:
            id_token = body.id_token
            
        if not id_token:
            return {"code": "BAD_REQUEST", "message": "id_token is required"}, 400
            
        # Get dev email header if present
        x_dev_email = request.headers.get('x-dev-email')
        
        email = None
        google_sub = None
        name = None
        avatar = None

        # Dev bypass (ONLY if enabled)
        dev_auth_bypass = os.getenv("DEV_AUTH_BYPASS", "false").lower() == "true"
        if dev_auth_bypass and x_dev_email:
            email = x_dev_email
            google_sub = f"dev-{x_dev_email}"
            name = x_dev_email.split("@")[0]
        else:
            try:
                claims = verify_google_id_token(id_token)
                email = claims.get("email")
                google_sub = claims.get("sub")
                name = claims.get("name")
                avatar = claims.get("picture")

                if not email:
                    raise ValueError("Email not found in token")
            except Exception as e:
                return {"code": "BAD_REQUEST", "message": f"Invalid Google token: {e}"}, 400

        # Get database session
        db = get_db_session()
        try:
            # Upsert user
            res = db.execute(select(User).where(User.email == email))
            user = res.scalar_one_or_none()
            if user is None:
                # In dev mode with dev bypass, automatically allow users
                is_allowed = dev_auth_bypass and x_dev_email is not None
                user = User(email=email, google_sub=google_sub, name=name, avatar_url=avatar, is_allowed=is_allowed)
                db.add(user)
                db.commit()
                db.refresh(user)
            else:
                # update google_sub/name/avatar if changed
                changed = False
                if google_sub and user.google_sub != google_sub:
                    user.google_sub = google_sub
                    changed = True
                if name and user.name != name:
                    user.name = name
                    changed = True
                if avatar and user.avatar_url != avatar:
                    user.avatar_url = avatar
                    changed = True
                # In dev mode with dev bypass, automatically allow users
                if dev_auth_bypass and x_dev_email is not None and not user.is_allowed:
                    user.is_allowed = True
                    changed = True
                if changed:
                    db.commit()

            if not user.is_allowed:
                return {"code": "FORBIDDEN", "message": "User is not allowed. Contact an administrator."}, 403

            access, refresh = issue_tokens(str(user.id))
            return {
                "access_token": access,
                "refresh_token": refresh,
                "token_type": "Bearer",
                "expires_in": int(os.getenv("ACCESS_TOKEN_EXPIRES_SECONDS", "3600")),
                "user": {
                    "id": str(user.id),
                    "email": user.email,
                    "google_sub": user.google_sub,
                    "name": user.name,
                    "avatar_url": user.avatar_url,
                    "is_allowed": user.is_allowed,
                }
            }, 200
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in google_verify: {e}")
        return {"code": "INTERNAL_ERROR", "message": "Internal server error"}, 500

def refresh_token(body):
    """Refresh access token"""
    try:
        # Parse request body
        if isinstance(body, dict):
            refresh_token_str = body.get('refresh_token')
        else:
            refresh_token_str = body.refresh_token
            
        if not refresh_token_str:
            return {"code": "BAD_REQUEST", "message": "refresh_token is required"}, 400
            
        try:
            # Verify refresh token
            jwt_refresh_secret = os.getenv("JWT_REFRESH_SECRET", "dev-refresh-secret-change-me-in-production")
            payload = jwt.decode(
                refresh_token_str,
                jwt_refresh_secret,
                algorithms=["HS256"],
                audience="sirev-proposal-users",
                issuer="sirev-proposal"
            )

            if payload.get("type") != "refresh":
                return {"code": "UNAUTHORIZED", "message": "Invalid token type"}, 401

            user_id = payload.get("sub")
            if not user_id:
                return {"code": "UNAUTHORIZED", "message": "Invalid token"}, 401

            # Get user from database (convert string user_id to integer)
            try:
                user_id_int = int(user_id)
            except (ValueError, TypeError):
                return {"code": "UNAUTHORIZED", "message": "Invalid user ID in token"}, 401

            # Get database session
            db = get_db_session()
            try:
                res = db.execute(select(User).where(User.id == user_id_int))
                user = res.scalar_one_or_none()
                if not user or not user.is_allowed:
                    return {"code": "UNAUTHORIZED", "message": "User not found or not allowed"}, 401

                # Generate new access token
                access_token = create_access_token(user_id)

                return {
                    "access_token": access_token,
                    "refresh_token": refresh_token_str,  # Keep the same refresh token
                    "token_type": "Bearer",
                    "expires_in": int(os.getenv("ACCESS_TOKEN_EXPIRES_SECONDS", "3600")),
                    "user": {
                        "id": str(user.id),
                        "email": user.email,
                        "google_sub": user.google_sub,
                        "name": user.name,
                        "avatar_url": user.avatar_url,
                        "is_allowed": user.is_allowed,
                    }
                }, 200
            finally:
                db.close()

        except jwt.ExpiredSignatureError:
            return {"code": "UNAUTHORIZED", "message": "Refresh token expired"}, 401
        except jwt.JWTError:
            return {"code": "UNAUTHORIZED", "message": "Invalid refresh token"}, 401
            
    except Exception as e:
        logger.error(f"Error in refresh_token: {e}")
        env = os.getenv("ENV", "production")
        if env == "dev":
            import traceback
            return {
                "code": "INTERNAL_ERROR",
                "message": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }, 500
        else:
            return {"code": "INTERNAL_ERROR", "message": "Internal server error"}, 500

def me():
    """Get current user"""
    try:
        # Get current user (sync version for Flask)
        from flask import request
        from app.services.tokens import verify_token
        from sqlalchemy import select
        from app.models import User

        # Get authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return {"code": "UNAUTHORIZED", "message": "Missing/invalid token"}, 401

        token = auth_header[7:]  # Remove 'Bearer ' prefix

        try:
            payload = verify_token(token, scope="access")
        except Exception:
            return {"code": "UNAUTHORIZED", "message": "Invalid token"}, 401

        user_id = payload.get("sub")
        if not user_id:
            return {"code": "UNAUTHORIZED", "message": "Invalid token"}, 401

        # Convert string user_id to integer
        try:
            user_id_int = int(user_id)
        except (ValueError, TypeError):
            return {"code": "UNAUTHORIZED", "message": "Invalid user ID in token"}, 401

        # Get database session and fetch user
        db = get_db_session()
        try:
            res = db.execute(select(User).where(User.id == user_id_int))
            current_user = res.scalar_one_or_none()
            if not current_user or not current_user.is_allowed:
                return {"code": "UNAUTHORIZED", "message": "User not found or not allowed"}, 401
        finally:
            db.close()

        return {
            "id": str(current_user.id),
            "email": current_user.email,
            "google_sub": current_user.google_sub,
            "name": current_user.name,
            "avatar_url": current_user.avatar_url,
            "is_allowed": current_user.is_allowed,
        }, 200
        
    except Exception as e:
        logger.error(f"Error in me: {e}")
        env = os.getenv("ENV", "production")
        if env == "dev":
            import traceback
            return {
                "code": "INTERNAL_ERROR",
                "message": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }, 500
        else:
            return {"code": "INTERNAL_ERROR", "message": "Internal server error"}, 500

# Sync functions for direct use (no wrappers needed)
def google_verify_sync(body):
    """Direct call to synchronous google_verify"""
    return google_verify(body)

def refresh_token_sync(body):
    """Direct call to synchronous refresh_token"""
    return refresh_token(body)

def me_sync():
    """Direct call to synchronous me"""
    return me()
