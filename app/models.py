from __future__ import annotations
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship
from sqlalchemy import String, Boolean, ForeignKey, Integer, Numeric, BigInteger, Text, DateTime, Date, UniqueConstraint
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>
from datetime import datetime
from sqlalchemy import func

class Base(DeclarativeBase):
    pass

class User(Base):
    __tablename__ = "users"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    email: Mapped[str] = mapped_column(String(320), unique=True, nullable=False)
    google_sub: Mapped[str | None] = mapped_column(String(64), unique=True)
    name: Mapped[str | None] = mapped_column(String(200))
    avatar_url: Mapped[str | None] = mapped_column(String(500))
    is_allowed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    created_at: Mapped[datetime] = mapped_column(server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(server_default=func.now(), onupdate=func.now())

    links: Mapped[list["ShareLink"]] = relationship(back_populates="user")

class ShareLink(Base):
    __tablename__ = "share_links"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token: Mapped[str] = mapped_column(String(140), unique=True, nullable=False)
    title: Mapped[str | None] = mapped_column(String(200))
    note: Mapped[str | None] = mapped_column(Text)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    expires_at: Mapped[datetime | None]
    created_at: Mapped[datetime] = mapped_column(server_default=func.now())

    user: Mapped["User"] = relationship(back_populates="links")
    items: Mapped[list["ShareLinkTrip"]] = relationship(back_populates="share_link", cascade="all, delete-orphan")

class Hotel(Base):
    __tablename__ = "hotels"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    name: Mapped[str] = mapped_column(String(300), nullable=False, index=True)
    source_hotel_id: Mapped[str | None] = mapped_column(String(200), index=True)
    address: Mapped[str | None] = mapped_column(String(500))
    city: Mapped[str | None] = mapped_column(String(120))
    country: Mapped[str | None] = mapped_column(String(100))

    # TripAdvisor details
    description: Mapped[str | None] = mapped_column(Text)
    ranking_out_of: Mapped[int | None]
    ranking: Mapped[int | None]
    rating: Mapped[float | None]
    num_reviews: Mapped[int | None]
    subratings: Mapped[dict | None] = mapped_column(JSON)
    amenities_data: Mapped[list | None] = mapped_column("amenities", JSON)
    web_url: Mapped[str | None] = mapped_column(String(500))
    see_all_photos: Mapped[str | None] = mapped_column(String(500))
    video_link: Mapped[str | None] = mapped_column(String(500))

    trips: Mapped[list["Trip"]] = relationship(back_populates="hotel", cascade="all, delete-orphan")
    images: Mapped[list["HotelImage"]] = relationship(back_populates="hotel", cascade="all, delete-orphan")
    amenities: Mapped[list["HotelAmenity"]] = relationship(back_populates="hotel", cascade="all, delete-orphan")
    reviews: Mapped[list["HotelReview"]] = relationship(back_populates="hotel", cascade="all, delete-orphan")

class Trip(Base):
    __tablename__ = "trips"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    hotel_id: Mapped[int] = mapped_column(Integer, ForeignKey("hotels.id", ondelete="CASCADE"), nullable=False)

    nights: Mapped[int | None]
    tour_operator: Mapped[str | None] = mapped_column(String(50))
    outbound_date: Mapped[str | None] = mapped_column(String(20))
    outbound_dep_time: Mapped[str | None] = mapped_column(String(20))
    outbound_flight: Mapped[str | None] = mapped_column(String(20))
    outbound_arr_time: Mapped[str | None] = mapped_column(String(20))
    return_date: Mapped[str | None] = mapped_column(String(20))
    return_dep_time: Mapped[str | None] = mapped_column(String(20))
    return_flight: Mapped[str | None] = mapped_column(String(20))
    return_arr_time: Mapped[str | None] = mapped_column(String(20))
    price: Mapped[float | None]
    room_desc: Mapped[str | None] = mapped_column(Text)
    comment: Mapped[str | None] = mapped_column(Text)
    people_per_trip: Mapped[str | None] = mapped_column(String(100))
    full_price: Mapped[float | None]

    created_at: Mapped[datetime] = mapped_column(server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(server_default=func.now(), onupdate=func.now())

    hotel: Mapped["Hotel"] = relationship(back_populates="trips")

class ShareLinkTrip(Base):
    __tablename__ = "share_link_trips"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    share_link_id: Mapped[int] = mapped_column(Integer, ForeignKey("share_links.id", ondelete="CASCADE"), nullable=False)
    trip_id: Mapped[int] = mapped_column(Integer, ForeignKey("trips.id", ondelete="CASCADE"), nullable=False)
    position: Mapped[int] = mapped_column(Integer, nullable=False)

    created_at: Mapped[datetime] = mapped_column(server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(server_default=func.now(), onupdate=func.now())

    share_link: Mapped["ShareLink"] = relationship(back_populates="items")
    trip: Mapped["Trip"] = relationship()

class HotelImage(Base):
    __tablename__ = "hotel_images"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    hotel_id: Mapped[int] = mapped_column(Integer, ForeignKey("hotels.id", ondelete="CASCADE"), nullable=False)
    url: Mapped[str] = mapped_column(String(1000), nullable=False)
    width: Mapped[int | None]
    height: Mapped[int | None]
    attribution: Mapped[str | None] = mapped_column(String(500))
    provider: Mapped[str] = mapped_column(String(50), nullable=False)
    created_at: Mapped[datetime] = mapped_column(server_default=func.now())

    hotel: Mapped["Hotel"] = relationship(back_populates="images")

class HotelAmenity(Base):
    __tablename__ = "hotel_amenities"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    hotel_id: Mapped[int] = mapped_column(Integer, ForeignKey("hotels.id", ondelete="CASCADE"), nullable=False)
    name: Mapped[str] = mapped_column(String(200), nullable=False)
    provider: Mapped[str] = mapped_column(String(50), nullable=False)
    created_at: Mapped[datetime] = mapped_column(server_default=func.now())

    hotel: Mapped["Hotel"] = relationship(back_populates="amenities")

class HotelReview(Base):
    __tablename__ = "hotel_reviews"
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    hotel_id: Mapped[int] = mapped_column(Integer, ForeignKey("hotels.id", ondelete="CASCADE"), nullable=False)
    review_id: Mapped[str | None] = mapped_column(String(200))
    reviewer_name: Mapped[str | None] = mapped_column(String(200))
    rating: Mapped[float | None]
    text: Mapped[str] = mapped_column(Text, nullable=False)
    date: Mapped[datetime | None]
    url: Mapped[str | None] = mapped_column(String(1000))
    provider: Mapped[str] = mapped_column(String(50), nullable=False)
    created_at: Mapped[datetime] = mapped_column(server_default=func.now())

    hotel: Mapped["Hotel"] = relationship(back_populates="reviews")
