from __future__ import annotations
from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON>pen<PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.db import get_session
from app.models import User
from app.schemas import GoogleVerifyRequest, AuthSuccess, RefreshRequest, RefreshResponse, UserOut
import os
from dotenv import load_dotenv
from app.services.tokens import issue_tokens, verify_token, create_access_token
from app.auth import verify_google_id_token, get_current_user
from jose import jwt

# Ensure .env is loaded
load_dotenv()

router = APIRouter(prefix="/auth", tags=["Auth"])

@router.post("/google/verify", response_model=AuthSuccess)
async def google_verify(body: GoogleVerifyRequest, db: AsyncSession = Depends(get_session), x_dev_email: str | None = Header(None, alias="x-dev-email")):
    email = None
    google_sub = None
    name = None
    avatar = None

    # Dev bypass (ONLY if enabled)
    dev_auth_bypass = os.getenv("DEV_AUTH_BYPASS", "false").lower() == "true"
    if dev_auth_bypass and x_dev_email:
        email = x_dev_email
        google_sub = f"dev-{x_dev_email}"
        name = x_dev_email.split("@")[0]
    else:
        try:
            claims = verify_google_id_token(body.id_token)
            email = claims.get("email")
            google_sub = claims.get("sub")
            name = claims.get("name")
            avatar = claims.get("picture")

            if not email:
                raise ValueError("Email not found in token")
        except Exception as e:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid Google token: {e}")

    # Upsert user
    res = await db.execute(select(User).where(User.email == email))
    user = res.scalar_one_or_none()
    if user is None:
        # In dev mode with dev bypass, automatically allow users
        is_allowed = dev_auth_bypass and x_dev_email is not None
        user = User(email=email, google_sub=google_sub, name=name, avatar_url=avatar, is_allowed=is_allowed)
        db.add(user)
        await db.commit()
        await db.refresh(user)
    else:
        # update google_sub/name/avatar if changed
        changed = False
        if google_sub and user.google_sub != google_sub:
            user.google_sub = google_sub
            changed = True
        if name and user.name != name:
            user.name = name
            changed = True
        if avatar and user.avatar_url != avatar:
            user.avatar_url = avatar
            changed = True
        # In dev mode with dev bypass, automatically allow users
        if dev_auth_bypass and x_dev_email is not None and not user.is_allowed:
            user.is_allowed = True
            changed = True
        if changed:
            await db.commit()

    if not user.is_allowed:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="User is not allowed. Contact an administrator.")

    access, refresh = issue_tokens(str(user.id))
    return AuthSuccess(
        access_token=access,
        refresh_token=refresh,
        expires_in=int(os.getenv("ACCESS_TOKEN_EXPIRES_SECONDS", "3600")),
        user=UserOut(
            id=str(user.id),
            email=user.email,
            google_sub=user.google_sub,
            name=user.name,
            avatar_url=user.avatar_url,
            is_allowed=user.is_allowed,
        ),
    )



@router.post("/refresh", response_model=AuthSuccess)
async def refresh_token(body: RefreshRequest, db: AsyncSession = Depends(get_session)):
    try:
        # Verify refresh token
        jwt_refresh_secret = os.getenv("JWT_REFRESH_SECRET", "dev-refresh-secret-change-me-in-production")
        payload = jwt.decode(
            body.refresh_token,
            jwt_refresh_secret,
            algorithms=["HS256"],
            audience="sirev-proposal-users",
            issuer="sirev-proposal"
        )

        if payload.get("type") != "refresh":
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token type")

        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

        # Get user from database (convert string user_id to integer)
        try:
            user_id_int = int(user_id)
        except (ValueError, TypeError):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid user ID in token")

        res = await db.execute(select(User).where(User.id == user_id_int))
        user = res.scalar_one_or_none()
        if not user or not user.is_allowed:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found or not allowed")

        # Generate new access token
        access_token = create_access_token(user_id)

        return AuthSuccess(
            access_token=access_token,
            refresh_token=body.refresh_token,  # Keep the same refresh token
            expires_in=int(os.getenv("ACCESS_TOKEN_EXPIRES_SECONDS", "3600")),
            user=UserOut(
                id=str(user.id),
                email=user.email,
                google_sub=user.google_sub,
                name=user.name,
                avatar_url=user.avatar_url,
                is_allowed=user.is_allowed,
            )
        )

    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Refresh token expired")
    except jwt.JWTError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token")

@router.get("/me", response_model=UserOut)
async def me(current_user: User = Depends(get_current_user)):
    return UserOut(
        id=str(current_user.id),
        email=current_user.email,
        google_sub=current_user.google_sub,
        name=current_user.name,
        avatar_url=current_user.avatar_url,
        is_allowed=current_user.is_allowed,
    )
