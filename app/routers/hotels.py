from __future__ import annotations

import time
from typing import List, Tuple, Dict

from dotenv import load_dotenv
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from app.db import get_session
from app.models import User, ShareLink, Hotel, Trip, ShareLinkTrip, HotelImage, HotelAmenity, HotelReview
from app.schemas import TripSubmit, SubmitTripsResponse
from app.auth import get_current_user
from urllib.parse import urljoin
import secrets, logging, asyncio, re
from datetime import datetime, date

from app.services.tripadvisor import TripAdvisorClient

router = APIRouter(tags=["Hotels & Share"])
logger = logging.getLogger("api.hotels")


def gen_token() -> str:
    return secrets.token_urlsafe(18)

def parse_location(location: str) -> Tuple[str | None, str | None]:
    if not location:
        return None, None

    # Remove extra text in parentheses like "(view the guide)"
    location = re.sub(r'\s*\([^)]*\)\s*', '', location).strip()

    parts = [p.strip() for p in location.split(",") if p.strip()]
    if len(parts) == 1:
        return None, clean_html_tags(parts[0])
    return clean_html_tags(parts[0]), clean_html_tags(parts[-1])  # city, country

def clean_html_tags(text: str | None) -> str | None:
    """Remove all HTML tags except <br> (any of <br>, <br/>, <br />) and clean up whitespace."""
    if not text:
        return None

    # Normalize all <br> variants to a canonical "<br>"
    normalized = re.sub(r'<\s*br\s*/?\s*>', '<br>', text, flags=re.IGNORECASE)

    # Remove every other HTML tag (leave <br> intact)
    without_tags = re.sub(r'</?(?!br\b)[^>]+>', '', normalized, flags=re.IGNORECASE)

    # Clean up whitespace (keeps the literal "<br>" tokens)
    cleaned = re.sub(r'\s+', ' ', without_tags).strip()

    return cleaned or None

def parse_review_date(date_string: str | None) -> date | None:
    """Convert ISO datetime string to date object"""
    if not date_string:
        return None
    try:
        # Parse ISO format like "2025-09-01T10:55:34Z"
        dt = datetime.fromisoformat(date_string.replace('Z', '+00:00'))
        # Return just the date part as a date object
        return dt.date()
    except (ValueError, AttributeError):
        # If parsing fails, return None
        return None

def clean_room_desc(text: str | None) -> str | None:
    def clean_string(text: str) -> str:
        stops = ["room", "A Sale For the Spontaneous", "Free upgrade special"]

        text = text.strip()

        # Trim each stop word if it appears at start or end
        for stop in stops:
            if text.lower().startswith(stop.lower()):
                text = text[len(stop):].strip()
            if text.lower().endswith(stop.lower()):
                text = text[:-len(stop)].strip()

        return text

    """Clean up room description"""
    if not text:
        return None
    clean_text = re.sub(r'\s+', ' ', text).strip()
    return clean_text if clean_text else None

def clean_all_special_spaces(text: str) -> str:
    return re.sub(r" ", " ", text, flags=re.UNICODE).strip()


def convert_trip_operator(code: str) -> str:
    TRIP_OPERATOR_NAMES: Dict[str, str] = {
        "CAH": "Caribe Sol",
        "CLM": "Club Med",
        "ETG": "Enjoy Travel Group",
        "HOL": "Holasun",
        "POR": "Porter Escapes",
        "SQV": "Sunquest Vacations",
        "STC": "The Special Travel",
        "SWG": "Sunwing Vacations",
        "TBA": "TravelBrands",
        "VAC": "Air Canada Vacations",
        "VAT": "Transat",
        "VWQ": "Vacances Westjet Quebec",
        "WJS": "WestJet Vacations",
    }

    """
    Convert a left-hand code (e.g., 'CAH') to its name (e.g., 'Caribe Sol').

    - Case/whitespace-insensitive.
    - Returns the original code if not found in mapping.
    """
    try:
        return TRIP_OPERATOR_NAMES[code.strip().upper()]
    except Exception:
        # Return the original code if not found in mapping
        return code

@router.post("/hotel-trips:bulk", response_model=SubmitTripsResponse)
async def submit_trips_bulk(body: List[TripSubmit], request: Request, db: AsyncSession = Depends(get_session), user: User = Depends(get_current_user)):
    load_dotenv()
    if not isinstance(body, list) or len(body) == 0:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Expected a non-empty JSON array.")
    if len(body) > 50:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Too many trips in one request (max 50).")

    logger.info("/hotel-trips:bulk received %d trips from user=%s", len(body), user.email)

    # 1) Create share link
    token = gen_token()
    link = ShareLink(user_id=user.id, token=token, title=None, note=None, is_active=True)
    db.add(link)
    await db.flush()

    tripadvisor = TripAdvisorClient()

    trips_created: List[Trip] = []
    try:
        for idx, trip_in in enumerate(body):
            city, country = parse_location(trip_in.location or "")
            # 2) Find or create Hotel by (name, city, country)
            stmt = select(Hotel).where(Hotel.name == trip_in.name)
            if city:
                stmt = stmt.where(Hotel.city == city)
            if country:
                stmt = stmt.where(Hotel.country == country)
            res = await db.execute(stmt)
            hotel = res.scalar_one_or_none()

            if hotel is None:
                logger.info("Hotel not found locally. Searching TripAdvisor name=%s location=%s", trip_in.name, trip_in.location)
                ta_hit = await tripadvisor.search_hotel(trip_in.name, trip_in.location or "")
                if not ta_hit:
                    logger.warning("TripAdvisor search returned no results for %s / %s", trip_in.name, trip_in.location)
                    # Create minimal hotel from provided data
                    hotel = Hotel(name=trip_in.name, source_hotel_id=None, address=None, city=city, country=country)
                else:
                    # Extract fields from TripAdvisor response - prioritize location_id and address_string
                    source_id = str(ta_hit.get("location_id") or ta_hit.get("id") or ta_hit.get("hotel_id") or "")

                    # Check if hotel already exists by source_hotel_id
                    if source_id:
                        existing_by_source = await db.execute(select(Hotel).where(Hotel.source_hotel_id == source_id))
                        hotel = existing_by_source.scalar_one_or_none()
                        if hotel:
                            logger.info(f"Found existing hotel by source_hotel_id: {source_id}")
                        else:
                            # Create new hotel with TripAdvisor data
                            addr = ta_hit.get("address_string") or ta_hit.get("address") or ta_hit.get("address_obj", {}).get("address_string")
                            cty = ta_hit.get("address_obj", {}).get("city") or city
                            ctry = ta_hit.get("address_obj", {}).get("country") or country

                            # Create hotel with TripAdvisor details (with proper type conversion)
                            def safe_int(value):
                                try:
                                    return int(value) if value is not None else None
                                except (ValueError, TypeError):
                                    return None

                            def safe_float(value):
                                try:
                                    return float(value) if value is not None else None
                                except (ValueError, TypeError):
                                    return None

                            hotel = Hotel(
                                name=trip_in.name,
                                source_hotel_id=source_id or None,
                                address=addr,
                                city=cty,
                                country=ctry,
                                description=ta_hit.get("description"),
                                ranking_out_of=safe_int(ta_hit.get("ranking_out_of")),
                                ranking=safe_int(ta_hit.get("ranking")),
                                rating=safe_float(ta_hit.get("rating")),
                                num_reviews=safe_int(ta_hit.get("num_reviews")),
                                subratings=ta_hit.get("subratings"),
                                amenities_data=ta_hit.get("amenities"),
                                web_url=ta_hit.get("web_url"),
                                see_all_photos=ta_hit.get("see_all_photos"),
                                video_link=trip_in.video_link if hasattr(trip_in, 'video_link') else None
                            )
                            db.add(hotel)
                            await db.flush()
                    else:
                        # Create hotel without source_hotel_id
                        addr = ta_hit.get("address_string") or ta_hit.get("address") or ta_hit.get("address_obj", {}).get("address_string")
                        cty = ta_hit.get("address_obj", {}).get("city") or city
                        ctry = ta_hit.get("address_obj", {}).get("country") or country
                        hotel = Hotel(name=trip_in.name, address=addr, city=cty, country=ctry)
                        db.add(hotel)
                        await db.flush()
                db.add(hotel)
                await db.flush()

                # Fetch images & reviews if we have a source id
                if hotel.source_hotel_id:
                    try:
                        time.sleep(1)  # Rate limit to 2 requests per second
                        images = await tripadvisor.get_hotel_images(hotel.source_hotel_id)
                        for im in images:
                            # Extract image URLs from the nested structure
                            images_data = im.get("images", {})
                            original = images_data.get("original", {})
                            large = images_data.get("large", {})
                            medium = images_data.get("medium", {})

                            # Prefer original, then large, then medium
                            url = original.get("url") or large.get("url") or medium.get("url")
                            if url:
                                # Get dimensions from the selected image size
                                width = original.get("width") or large.get("width") or medium.get("width")
                                height = original.get("height") or large.get("height") or medium.get("height")

                                db.add(HotelImage(
                                    hotel_id=hotel.id,
                                    url=url,
                                    width=width,
                                    height=height,
                                    attribution=im.get("caption") or None,
                                    provider="tripadvisor"
                                ))
                        reviews = await tripadvisor.get_hotel_reviews(hotel.source_hotel_id)
                        for rv in reviews[:20]:
                            review_date = parse_review_date(rv.get("published_date"))
                            review_text = (rv.get("title", "") + " \n " + rv.get("text", "")).strip()
                            db.add(HotelReview(
                                hotel_id=hotel.id,
                                review_id=str(rv.get("id")),
                                reviewer_name=rv.get("user.username"),
                                rating=rv.get("rating"),
                                text=review_text,
                                url=rv.get("url"),
                                provider="tripadvisor",
                                date=review_date
                            ))
                    except Exception as e:
                        logger.exception("TripAdvisor enrichment failed for hotel_id=%s: %s", hotel.id, e)
            else:
                logger.info("Found existing hotel id=%s for %s", hotel.id, hotel.name)

            # Update hotel video_link if provided
            if trip_in.video_link and hotel:
                hotel.video_link = trip_in.video_link
                db.add(hotel)

            # 3) Create Trip
            trip = Trip(
                hotel_id=hotel.id,
                nights=trip_in.nights,
                tour_operator=trip_in.tur_operator,  # Store original value
                outbound_date=clean_all_special_spaces(trip_in.outboundDate),
                outbound_dep_time=trip_in.outboundDepTime,
                outbound_flight=clean_all_special_spaces(trip_in.outboundFlight),
                outbound_arr_time=trip_in.outboundArrTime,
                return_date=clean_all_special_spaces(trip_in.returnDate),
                return_dep_time=trip_in.returnDepTime,
                return_flight=clean_all_special_spaces(trip_in.returnFlight),
                return_arr_time=trip_in.returnArrTime,
                price=trip_in.price,
                room_desc=clean_html_tags(clean_room_desc(trip_in.roomDesc)),
                comment=trip_in.comment,
                people_per_trip=trip_in.people_per_trip,
                full_price=trip_in.full_price,
            )
            db.add(trip)
            await db.flush()
            trips_created.append(trip)

            # 4) Link trip to share link (ordering preserved)
            db.add(ShareLinkTrip(share_link_id=link.id, trip_id=trip.id, position=idx))

        await db.commit()
    finally:
        await tripadvisor.aclose()

    # 5) Build share URL
    base_url = str(request.base_url).rstrip("/")
    share_url = f"{base_url}/s/{token}"

    logger.info("Created share link %s with %d trips", token, len(trips_created))
    return SubmitTripsResponse(share_url=share_url, link_token=token, link_id=str(link.id), trips_saved=len(trips_created))
