from pydantic_settings import BaseSettings
from pydantic import AnyHttpUrl, field_validator
from typing import List, Optional

class Settings(BaseSettings):
    APP_NAME: str = "Sirev Proposal Generator API"
    ENV: str = "dev"
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    LOG_LEVEL: str = "INFO"

    CORS_ALLOW_ORIGINS: str = "chrome-extension://<EXT_ID>,https://www.sirev.com,http://localhost:3000"

    DATABASE_URL: str = "postgresql+asyncpg://postgres:postgres@db:5432/sirev"

    GOOGLE_CLIENT_ID: str = "your-google-oauth-client-id.apps.googleusercontent.com"
    JWT_SECRET: str = "change-me"
    JWT_REFRESH_SECRET: str = "change-me-2"
    JWT_ISSUER: str = "sirev-proposal"
    JWT_AUDIENCE: str = "sirev-proposal-users"
    ACCESS_TOKEN_EXPIRES_SECONDS: int = 3600
    REFRESH_TOKEN_EXPIRES_SECONDS: int = 60*60*24*14

    DEV_AUTH_BYPASS: bool = True

    ENRICH_PROVIDER: str = "mock"
    TRIPADVISOR_BASE_URL: str = "https://api.tripadvisor.com"
    TRIPADVISOR_API_KEY: str = ""
    TRIPADVISOR_TIMEOUT_SECONDS: int = 8

    ENRICH_CONCURRENCY: int = 6
    ENRICH_PER_CALL_TIMEOUT_SECONDS: int = 5
    ENRICH_GLOBAL_TIMEOUT_SECONDS: int = 25

    @property
    def allowed_origins(self) -> List[str]:
        return [o.strip() for o in self.CORS_ALLOW_ORIGINS.split(",") if o.strip()]

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# settings = Settings()
