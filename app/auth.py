from __future__ import annotations
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from google.oauth2 import id_token as google_id_token
from google.auth.transport import requests as google_requests
from flask import request

import os
from app.db import get_session
from app.models import User
from app.services.tokens import verify_token

async def get_current_user_from_request() -> User | None:
    """Flask-compatible version of get_current_user"""
    # Get authorization header
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None

    token = auth_header[7:]  # Remove 'Bearer ' prefix

    try:
        payload = verify_token(token, scope="access")
    except Exception:
        return None

    user_id = payload.get("sub")
    if not user_id:
        return None

    # Convert string user_id to integer
    try:
        user_id_int = int(user_id)
    except (ValueError, TypeError):
        return None

    # Get database session
    db = await get_session().__anext__()
    try:
        res = await db.execute(select(User).where(User.id == user_id_int))
        user = res.scalar_one_or_none()
        if not user or not user.is_allowed:
            return None
        return user
    finally:
        await db.close()

def verify_google_id_token(id_token: str) -> dict:
    # In dev you may prefer to bypass via header checked in router.
    google_request = google_requests.Request()
    google_client_id = os.getenv("GOOGLE_CLIENT_ID", "456832608708-7g4m3l80bf4eujjpdqveg5efge75pkhh.apps.googleusercontent.com")
    claims = google_id_token.verify_oauth2_token(id_token, google_request, google_client_id)
    # Basic checks
    if not claims.get("email"):
        raise ValueError("Missing email in token")
    return claims
