from __future__ import annotations
import os
from dotenv import load_dotenv
import connexion
from flask import Flask
from flask_cors import CORS
from app.logging import configure_logging

# Load environment variables from .env file
load_dotenv()

configure_logging()

def create_app():
    # Create Connexion app
    import os
    spec_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    connexion_app = connexion.FlaskApp(__name__, specification_dir=spec_dir)

    # Add the OpenAPI specification
    connexion_app.add_api('openapi.yaml', validate_responses=False, strict_validation=False)

    # Get the underlying Flask app
    app = connexion_app.app
    app.config['TITLE'] = os.getenv("APP_NAME", "Sirev Proposal Generator API")

    # Configure debug mode based on environment
    env = os.getenv("ENV", "production")
    if env == "dev":
        app.config['DEBUG'] = True
        app.config['TESTING'] = True
    else:
        app.config['DEBUG'] = False

    # CORS
    cors_origins_str = os.getenv("CORS_ALLOW_ORIGINS", "chrome-extension://jaebfahjgbcggcfhjelcblfgkbikkadf,https://www.sirev.com,http://localhost:3000")
    allowed_origins = [o.strip() for o in cors_origins_str.split(",") if o.strip()]

    CORS(app,
         origins=allowed_origins,
         allow_headers=["Authorization", "Content-Type", "x-dev-email"],
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         supports_credentials=True,
         max_age=600)

    # Static files
    from flask import send_from_directory, make_response, request

    @app.route('/static/<path:filename>')
    def static_files(filename):
        return send_from_directory('app/static', filename)

    # Health check endpoint
    @app.route('/healthz')
    def healthz():
        return {"ok": True, "env": os.getenv("ENV", "dev")}

    # Temporary direct route for testing hotel bulk endpoint
    @app.route('/hotel-trips:bulk', methods=['POST', 'OPTIONS'])
    def hotel_trips_bulk_direct():
        from flask import request

        if request.method == 'OPTIONS':
            # Handle preflight request
            response = make_response()
            response.headers.add("Access-Control-Allow-Origin", "*")
            response.headers.add('Access-Control-Allow-Headers', "Authorization,Content-Type,x-dev-email")
            response.headers.add('Access-Control-Allow-Methods', "GET,PUT,POST,DELETE,OPTIONS")
            return response

        try:
            from app.controllers.hotels import submit_trips_bulk_sync
            return submit_trips_bulk_sync(request.get_json())
        except Exception as e:
            import traceback
            env = os.getenv("ENV", "production")
            if env == "dev":
                return {
                    "error": str(e),
                    "type": type(e).__name__,
                    "traceback": traceback.format_exc()
                }, 500
            else:
                return {"error": "Internal server error"}, 500

    # Temporary direct route for testing share page
    @app.route('/s/<token>', methods=['GET'])
    def share_page_direct(token):
        try:
            from app.controllers.public import share_page_sync
            return share_page_sync(token)
        except Exception as e:
            import traceback
            env = os.getenv("ENV", "production")
            if env == "dev":
                return {
                    "error": str(e),
                    "type": type(e).__name__,
                    "traceback": traceback.format_exc()
                }, 500
            else:
                return {"error": "Internal server error"}, 500

    # Temporary direct routes for auth endpoints
    @app.route('/auth/google/verify', methods=['POST', 'OPTIONS'])
    def google_verify_direct():
        from flask import request

        if request.method == 'OPTIONS':
            # Handle preflight request
            response = make_response()
            response.headers.add("Access-Control-Allow-Origin", "*")
            response.headers.add('Access-Control-Allow-Headers', "Authorization,Content-Type,x-dev-email")
            response.headers.add('Access-Control-Allow-Methods', "GET,PUT,POST,DELETE,OPTIONS")
            return response

        try:
            from app.controllers.auth import google_verify_sync
            return google_verify_sync(request.get_json())
        except Exception as e:
            import traceback
            env = os.getenv("ENV", "production")
            if env == "dev":
                return {
                    "error": str(e),
                    "type": type(e).__name__,
                    "traceback": traceback.format_exc()
                }, 500
            else:
                return {"error": "Internal server error"}, 500

    @app.route('/auth/refresh', methods=['POST'])
    def refresh_token_direct():
        try:
            from app.controllers.auth import refresh_token_sync
            from flask import request
            return refresh_token_sync(request.get_json())
        except Exception as e:
            import traceback
            env = os.getenv("ENV", "production")
            if env == "dev":
                return {
                    "error": str(e),
                    "type": type(e).__name__,
                    "traceback": traceback.format_exc()
                }, 500
            else:
                return {"error": "Internal server error"}, 500

    @app.route('/me', methods=['GET'])
    def me_direct():
        try:
            from app.controllers.auth import me_sync
            return me_sync()
        except Exception as e:
            import traceback
            env = os.getenv("ENV", "production")
            if env == "dev":
                return {
                    "error": str(e),
                    "type": type(e).__name__,
                    "traceback": traceback.format_exc()
                }, 500
            else:
                return {"error": "Internal server error"}, 500

    # Custom error handler for development
    @app.errorhandler(Exception)
    def handle_exception(e):
        import traceback
        env = os.getenv("ENV", "production")
        if env == "dev":
            # In development, return detailed error information
            return {
                "error": str(e),
                "type": type(e).__name__,
                "traceback": traceback.format_exc()
            }, 500
        else:
            # In production, return generic error
            return {"error": "Internal server error"}, 500

    return connexion_app

# Create the app instance
connexion_app = create_app()
app = connexion_app.app
