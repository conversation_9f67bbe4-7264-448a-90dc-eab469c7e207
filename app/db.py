from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
import os
import logging

logger = logging.getLogger("api.db")

# Convert async database URL to sync
database_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@db:5432/sirev")
# Replace asyncpg with psycopg2 for synchronous operations
sync_database_url = database_url.replace("+asyncpg", "").replace("postgresql://", "postgresql+psycopg2://")

# Configure synchronous engine with connection pooling for production
engine = create_engine(
    sync_database_url,
    pool_pre_ping=True,
    pool_size=5,
    max_overflow=10,
    pool_timeout=30,
    pool_recycle=3600,  # Recycle connections every hour
    echo=False  # Set to True for SQL debugging
)

SessionLocal = sessionmaker(bind=engine, expire_on_commit=False)

def get_session() -> Session:
    """Get synchronous database session"""
    try:
        session = SessionLocal()
        return session
    except Exception as e:
        logger.error(f"Database session error: {e}")
        raise
