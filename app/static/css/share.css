:root {
    --brand: #CC0000; /* primary red */
    --brand-weak: #ffe5e5; /* subtle red tint */
    --text: #1f2937; /* slate-800 */
    --muted: #6b7280; /* slate-500 */
    --ink: #111827; /* slate-900 */
    --bg: #ffffff;
    --card: #ffffff;
    --shadow: 0 6px 24px rgba(0, 0, 0, .08), 0 2px 6px rgba(0, 0, 0, .06);
    --radius: 18px;
    --maxw: 1100px;

    /* Font families */
    --font-sans:  Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
    /*--font-sans: 'Cy Grotesk', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;*/
    /*--font-serif: 'Playfair Display', Georgia, serif;*/
}

/* ----- Global Resets & Type ----- */
*, *::before, *::after {
    box-sizing: border-box;
}

html, body {
    height: 100%;
}

body {
    margin: 0;
    font-family: var(--font-sans);
    color: var(--text);
    background: var(--bg);
    line-height: 1.55;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3 {
    font-family: "Playfair Display", Georgia, Cambria, Cochin, serif;
    color: var(--ink);
    margin: 0 0 .4rem;
    line-height: 1.15;
}

h1 {
    font-size: clamp(36px, 6vw, 64px);
    letter-spacing: -0.02em;
}

h2 {
    font-size: clamp(24px, 3.4vw, 36px);
}

p {
    margin: 0 0 1rem;
}

a {
    color: inherit;
    text-decoration: none;
}

.container {
    width: min(100%, var(--maxw));
    margin-inline: auto;
    padding: 24px;
}

/* ----- Brand Header Bar (Sticky + contact & socials) ----- */
.brand-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: var(--brand);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(0, 0, 0, .1);
    font-size: 14px;
    color: white;
}

.brand-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: var(--maxw);
    margin: 0 auto;
    padding: 12px 24px;
    gap: 16px;
}

.brand-name {
    font-weight: 700;
    color: white;
    font-size: 16px;
    line-height: 1;
}

.brand-name h2 {
    font-size: 26px;
    color: white;
}

.brand-tag {
    font-size: 15px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 2px;
}

.brand-contact {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 6px;
}

.brand-contact .line {
    display: flex;
    gap: 16px;
    align-items: center;
}

.brand-contact .item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: rgba(255, 255, 255, 0.9);
}

.brand-contact .item svg {
    width: 16px;
    height: 16px;
    opacity: 0.8;
}

.brand-contact .item a {
    color: white;
    transition: color 0.2s;
}

.brand-contact .item a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.brand-social {
    display: flex;
    gap: 12px;
}

.brand-social a {
    display: flex;
    align-items: center;
    gap: 4px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    transition: color 0.2s;
}

.brand-social a:hover {
    color: white;
}

.brand-social svg {
    width: 14px;
    height: 14px;
}

/* ----- Intro Section (Full-page cover) ----- */
.intro {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    /*background: linear-gradient(135deg, var(--brand-weak) 0%, #d4d4d4 100%);*/
    background-image: url('/static/images/share-intro.jpg');
    background-position: center; /* or top, bottom, left, right */
    background-repeat: no-repeat;
    background-size: cover;
    text-align: center;
    position: relative;
}

.intro-card {
    max-width: 700px;
}

.intro-eyebrow {
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.intro h1 span {
    color: var(--brand);
}

.intro-sub {
    font-size: 18px;
    color: var(--muted);
    margin: 24px 0 32px;
    line-height: 1.6;
}

.cta-row {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    background: var(--brand);
    color: white;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s;
    border: 2px solid var(--brand);
}

.btn:hover {
    background: transparent;
    color: var(--brand);
}

/* ----- General Info Section ----- */
.general-info {
    padding: 80px 0;
    background: #fafafa;
}

.info-card {
    background: white;
    border-radius: var(--radius);
    padding: 48px;
    box-shadow: var(--shadow);
}

.grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 48px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 16px;
    margin-top: 0;
}

.info-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-list li {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
    line-height: 1.5;
}

.badge-check {
    color: #10b981;
    font-weight: 600;
    flex-shrink: 0;
}

.badge-x {
    color: #ef4444;
    font-weight: 600;
    flex-shrink: 0;
}

.small {
    font-size: 14px;
    color: var(--muted);
}

/* ----- Hotel Cards ----- */
.hotel {
    margin: 80px 0;
}

.hotel-card {
    background: white;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

/* ----- Gallery (5-up layout) ----- */
.gallery {
    display: grid;
    gap: 8px;
    aspect-ratio: 16/9;
    overflow: hidden;
}

.gallery.layout-5 {
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    grid-template-areas:
    "main g1 g2"
    "main g3 g4";
}

.gallery a {
    display: block;
    overflow: hidden;
    cursor: zoom-in;
    transition: transform 0.3s;
}

.gallery a:hover {
    transform: scale(1.02);
}

.gallery img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.g-main {
    grid-area: main;
}

.g-1 {
    grid-area: g1;
}

.g-2 {
    grid-area: g2;
}

.g-3 {
    grid-area: g3;
}

.g-4 {
    grid-area: g4;
}

/* ----- Hotel Header ----- */
.hotel-header {
    padding: 32px;
}

.hotel-header h2 {
    margin-bottom: 12px;
}

.meta-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--muted);
}

.meta {
    display: flex;
    align-items: center;
    gap: 6px;
}

.meta svg {
    width: 18px;
    height: 18px;
    opacity: 0.7;
}

.rating {
    display: flex;
    align-items: center;
    gap: 6px;
}

.stars {
    display: flex;
    gap: 2px;
}

.star {
    width: 14px;
    height: 14px;
    fill: #fbbf24;
}

.reviews {
    color: var(--brand);
    text-decoration: underline;
}

.dot {
    width: 4px;
    height: 4px;
    background: var(--muted);
    border-radius: 50%;
}

/* ----- Amenities ----- */
.amenities {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.amenities li {
    background: #f5f6fa;
    color: #4b5563;
    border: 1px solid #e5e7eb;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.amenities.collapsed {
    display: none;
}

.amenities-toggle {
    border: 1px solid var(--brand);
    background: var(--brand);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    margin-top: 12px;
    transition: all 0.2s;
}

.amenities-toggle:hover {
    background: #da7474;
    color: white;
}

/* ----- Details Section ----- */
.details {
    padding: 0 32px 32px;
    display: grid;
    gap: 24px;
}

.panel {
    background: #fafafa;
    border-radius: 12px;
    padding: 24px;
}

/* ----- Trip Table ----- */
.trip-table {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e5e7eb;
}

.trip-cell {
    text-align: center;
    padding: 12px;
    border-radius: 8px;
    background: #f9fafb;
}

.trip-cell.price {
    background: var(--brand-weak);
    border: 2px solid var(--brand);
    padding: 20px 12px;
}

.trip-cell .label {
    font-size: 12px;
    color: var(--muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    font-weight: 600;
}

.trip-cell .value {
    font-size: 16px;
    font-weight: 600;
    color: var(--ink);
    line-height: 1.2;
}

.trip-cell.price .value {
    font-size: 32px;
    color: var(--brand);
    font-weight: 700;
}

.trip-cell.price .value .small, .trip-cell.price2 .value .small {
    font-size: 14px;
    color: var(--muted);
    font-weight: 400;
    display: block;
    margin-top: 4px;
}

/* ----- Flights ----- */
.flights.two-col {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.flight {
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.route {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 12px;
}

.route svg {
    color: var(--brand);
}

.time-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    font-size: 14px;
}

.time-row div {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.time-row strong {
    color: var(--muted);
    font-weight: 500;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ----- Footer ----- */
.thanks {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 60px 24px;
    text-align: center;
    margin-top: 80px;
    border-top: 3px solid var(--brand);
    position: relative;
    font-weight: bold;
}

.thanks::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--brand);
    border-radius: 0 0 3px 3px;
}

.thanks p {
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
    font-size: 18px;
    color: var(--text);
    font-weight: 400;
}

.thanks a {
    color: var(--brand);
    text-decoration: none;
    font-weight: 600;
    border-bottom: 2px solid transparent;
    transition: border-color 0.2s;
}

.thanks a:hover {
    border-bottom-color: var(--brand);
}

.intro-card.container .intro-title {
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.what-included-title {
    font-size: 20px;
}



/* ----- Responsive ----- */
@media (max-width: 768px) {
    .brand-inner {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .brand-contact {
        align-items: flex-start;
    }

    .brand-contact .line {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .grid {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .info-card {
        padding: 32px 24px;
    }

    .hotel-header {
        padding: 24px;
    }

    .details {
        padding: 0 24px 24px;
    }

    .flights.two-col {
        grid-template-columns: 1fr;
    }

    .trip-table {
        grid-template-columns: 1fr;
    }

    .cta-row {
        flex-direction: column;
        align-items: center;
    }
}

/* ----- Print Styles ----- */
@media print {
    body {
        margin-top: 72px;
    }

    .btn {
        display: none !important;
    }

    .intro {
        min-height: auto;
        height: 100vh;
    }

    .gallery {
        break-inside: avoid;
    }

    .hotel-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .page-break {
        break-after: page;
    }

    /* Hide video and interactive elements in print */
    .hotel-video,
    .see-all-photos,
    .review-link {
        display: none !important;
    }
}

/* ----- New Elements Styles ----- */
.media-links {
    margin-top: 12px;
    text-align: center;
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.photos-link {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    color: var(--muted);
    text-decoration: none;
    font-size: 14px;
    padding: 8px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.photos-link:hover {
    color: var(--brand);
    border-color: var(--brand);
    background-color: var(--brand-weak);
}

.video-tour-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--brand);
    color: white;
    border: none;
    font-size: 14px;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(204, 0, 0, 0.2);
}

.video-tour-btn:hover {
    background: #b30000;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(204, 0, 0, 0.3);
}

.video-tour-btn svg {
    margin-left: -2px;
}

.review-link {
    margin-left: 8px;
    color: var(--brand);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.review-link:hover {
    text-decoration: underline;
}

.agency-comment {
    margin: 20px 0;
    padding: 16px;
    background-color: #faebd7;
    border-left: 4px solid var(--brand);
    border-radius: 8px;
}

.agency-comment h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ink);
}

.agency-comment p {
    margin: 0;
    color: var(--text);
    line-height: 1.6;
}

.hotel-video {
    margin: 24px 0;
}

.hotel-video h4 {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ink);
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

/* Video Modal Styles */
.video-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 10000;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.video-modal-content {
    position: relative;
    width: 100%;
    max-width: 900px;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.video-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.video-modal-close:hover {
    background: white;
    transform: scale(1.1);
}

.video-modal-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-modal-container iframe,
.video-modal-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.original-price, .discounted-price {
    letter-spacing: 2px;
}