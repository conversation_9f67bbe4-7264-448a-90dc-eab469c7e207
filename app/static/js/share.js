// Share page functionality
document.addEventListener('DOMContentLoaded', function() {

  // Initialize amenities toggles
  initAmenitiesToggle();

  // Initialize PhotoSwipe lightbox
  initPhotoSwipe();

});

/**
 * Open video modal with the provided video URL
 */
function openVideoModal(videoUrl) {
  const modal = document.getElementById('videoModal');
  const container = document.getElementById('videoContainer');

  // Clear previous content
  container.innerHTML = '';

  // Create video element based on URL type
  let videoElement = '';

  if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
    // Extract YouTube video ID
    let videoId = '';
    if (videoUrl.includes('youtu.be')) {
      videoId = videoUrl.split('/').pop().split('?')[0];
    } else {
      videoId = videoUrl.split('v=')[1].split('&')[0];
    }

    videoElement = `
      <iframe src="https://www.youtube.com/embed/${videoId}?autoplay=1"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowfullscreen>
      </iframe>
    `;
  } else if (videoUrl.includes('vimeo.com')) {
    // Extract Vimeo video ID
    const videoId = videoUrl.split('/').pop();

    videoElement = `
      <iframe src="https://player.vimeo.com/video/${videoId}?autoplay=1"
              frameborder="0"
              allow="autoplay; fullscreen; picture-in-picture"
              allowfullscreen>
      </iframe>
    `;
  } else {
    // Direct video file
    videoElement = `
      <video controls autoplay>
        <source src="${videoUrl}" type="video/mp4">
        Your browser does not support the video tag.
      </video>
    `;
  }

  container.innerHTML = videoElement;
  modal.style.display = 'flex';
  document.body.style.overflow = 'hidden';
}

/**
 * Close video modal
 */
function closeVideoModal() {
  const modal = document.getElementById('videoModal');
  const container = document.getElementById('videoContainer');

  // Clear video content to stop playback
  container.innerHTML = '';
  modal.style.display = 'none';
  document.body.style.overflow = 'auto';
}

/**
 * Initialize amenities toggle functionality
 */
function initAmenitiesToggle() {
  const toggleButtons = document.querySelectorAll('.amenities-toggle');
  
  toggleButtons.forEach(button => {
    button.addEventListener('click', function() {
      const hotelHeader = this.closest('.hotel-header');
      const visibleAmenities = hotelHeader.querySelector('.amenities:not(.collapsed)');
      const collapsedAmenities = hotelHeader.querySelector('.amenities.collapsed');
      
      const isExpanded = this.getAttribute('aria-expanded') === 'true';
      
      if (isExpanded) {
        // Collapse: show basic amenities, hide detailed ones
        visibleAmenities.style.display = 'flex';
        collapsedAmenities.style.display = 'none';
        this.textContent = 'Show all amenities';
        this.setAttribute('aria-expanded', 'false');
      } else {
        // Expand: hide basic amenities, show detailed ones
        visibleAmenities.style.display = 'none';
        collapsedAmenities.style.display = 'flex';
        this.textContent = 'Show fewer amenities';
        this.setAttribute('aria-expanded', 'true');
      }
    });
  });
}

/**
 * Initialize PhotoSwipe lightbox for image galleries
 */
function initPhotoSwipe() {
  // Check if PhotoSwipe is available
  if (typeof PhotoSwipeLightbox === 'undefined') {
    console.warn('PhotoSwipe not loaded, using fallback image behavior');
    return;
  }
  
  try {
    const lightbox = new PhotoSwipeLightbox({
      gallery: '.gallery',
      children: 'a',
      pswpModule: () => import('https://unpkg.com/photoswipe@5/dist/photoswipe.esm.js'),
      // Additional options
      padding: { top: 20, bottom: 40, left: 100, right: 100 },
      bgOpacity: 0.9,
      showHideAnimationType: 'zoom',
      showAnimationDuration: 333,
      hideAnimationDuration: 333
    });
    
    lightbox.init();
    
    // Add keyboard navigation
    lightbox.on('uiRegister', function() {
      lightbox.pswp.keyboard.addKey(27, 'close'); // ESC key
    });
    
  } catch (error) {
    console.error('Failed to initialize PhotoSwipe:', error);
  }
}

/**
 * Smooth scroll to anchor links
 */
function initSmoothScroll() {
  const links = document.querySelectorAll('a[href^="#"]');
  
  links.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      
      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);
      
      if (targetElement) {
        const headerOffset = 80; // Account for fixed header
        const elementPosition = targetElement.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
        
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    });
  });
}

/**
 * Add loading states for images
 */
function initImageLoading() {
  const images = document.querySelectorAll('.gallery img');
  
  images.forEach(img => {
    if (img.complete) {
      img.classList.add('loaded');
    } else {
      img.addEventListener('load', function() {
        this.classList.add('loaded');
      });
      
      img.addEventListener('error', function() {
        this.classList.add('error');
        console.warn('Failed to load image:', this.src);
      });
    }
  });
}

/**
 * Print functionality
 */
function initPrintSupport() {
  // Add print button if needed
  const printButton = document.querySelector('.print-btn');
  if (printButton) {
    printButton.addEventListener('click', function() {
      window.print();
    });
  }
  
  // Handle print events
  window.addEventListener('beforeprint', function() {
    // Expand all amenities for print
    document.querySelectorAll('.amenities.collapsed').forEach(amenities => {
      amenities.style.display = 'flex';
    });
    document.querySelectorAll('.amenities:not(.collapsed)').forEach(amenities => {
      amenities.style.display = 'none';
    });
  });
  
  window.addEventListener('afterprint', function() {
    // Reset amenities display after print
    document.querySelectorAll('.amenities.collapsed').forEach(amenities => {
      amenities.style.display = 'none';
    });
    document.querySelectorAll('.amenities:not(.collapsed)').forEach(amenities => {
      amenities.style.display = 'flex';
    });
  });
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
  initSmoothScroll();
  initImageLoading();
  initPrintSupport();
});

// Export functions for potential external use
window.SharePageUtils = {
  initAmenitiesToggle,
  initPhotoSwipe,
  initSmoothScroll,
  initImageLoading,
  initPrintSupport
};
