<svg width="1600" height="1067" viewBox="0 0 1600 1067" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#bg)"/>
  <g transform="translate(800,533.5)">
    <!-- Hotel icon -->
    <rect x="-60" y="-40" width="120" height="80" fill="#d1d5db" rx="8"/>
    <rect x="-50" y="-30" width="20" height="20" fill="#9ca3af" rx="2"/>
    <rect x="-20" y="-30" width="20" height="20" fill="#9ca3af" rx="2"/>
    <rect x="10" y="-30" width="20" height="20" fill="#9ca3af" rx="2"/>
    <rect x="40" y="-30" width="20" height="20" fill="#9ca3af" rx="2"/>
    <rect x="-50" y="0" width="20" height="20" fill="#9ca3af" rx="2"/>
    <rect x="-20" y="0" width="20" height="20" fill="#9ca3af" rx="2"/>
    <rect x="10" y="0" width="20" height="20" fill="#9ca3af" rx="2"/>
    <rect x="40" y="0" width="20" height="20" fill="#9ca3af" rx="2"/>
    <rect x="-10" y="25" width="20" height="15" fill="#6b7280" rx="2"/>
    <!-- Text -->
    <text x="0" y="60" text-anchor="middle" font-family="Inter, sans-serif" font-size="24" fill="#6b7280">Hotel Image</text>
  </g>
</svg>
