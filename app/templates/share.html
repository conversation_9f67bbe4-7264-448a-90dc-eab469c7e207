<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>{{ title }} — Proposal</title>
  <meta name="robots" content="noindex,nofollow">
  <style>
    body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif; margin: 0; color: #111827; background: #fff; }
    header { padding: 18px 20px; border-bottom: 1px solid #e5e7eb; background: #f9fafb; }
    h1 { margin: 0 0 6px 0; font-size: 20px; }
    .container { max-width: 960px; margin: 0 auto; padding: 16px; }
    .note { color: #6b7280; margin-bottom: 16px; }
    .hotel { border: 1px solid #e5e7eb; border-radius: 12px; padding: 12px; margin-bottom: 16px; }
    .hotel h2 { margin: 0 0 8px 0; font-size: 18px; }
    .row { display: grid; grid-template-columns: 160px 1fr; gap: 8px; margin: 4px 0; }
    .k { color: #6b7280; font-size: 12px; }
    .v { font-size: 14px; }
    .images { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 8px; }
    .images img { border-radius: 8px; max-height: 140px; }
    .amenities { margin-top: 8px; color: #374151; font-size: 13px; }
    .reviews { margin-top: 8px; }
    .review { margin-bottom: 6px; font-size: 13px; }
    footer { color: #9ca3af; font-size: 12px; padding: 18px 20px; border-top: 1px solid #e5e7eb; }
  </style>
  <meta property="og:title" content="{{ title }} — Proposal" />
  <meta property="og:type" content="website" />
</head>
<body>
  <header>
    <div class="container">
      <h1>{{ title }}</h1>
      {% if note %}<div class="note">{{ note }}</div>{% endif %}
      <div class="note">Share token: {{ token }}</div>
    </div>
  </header>

  <main class="container">
    {% for block in trips %}
      <section class="hotel">
        <h2>{{ block.hotel.name }}</h2>
        <div class="row"><div class="k">Address</div><div class="v">{{ block.hotel.address or '' }}, {{ block.hotel.city or '' }}, {{ block.hotel.country or '' }}</div></div>
        {% if block.trip.room_desc %}<div class="row"><div class="k">Room</div><div class="v">{{ block.trip.room_desc }}</div></div>{% endif %}
        {% if block.trip.comment %}<div class="note">Comment: {{ block.trip.comment }}</div>{% endif %}
        <div class="row"><div class="k">Nights</div><div class="v">{{ block.trip.nights or '-' }}</div></div>
        <div class="row"><div class="k">Tour operator</div><div class="v">{{ block.trip.tour_operator or '-' }}</div></div>
        <div class="row"><div class="k">Outbound</div><div class="v">{{ block.trip.outboundDate }} {{ block.trip.outboundDepTime }} ({{ block.trip.outboundFlight }}) → {{ block.trip.outboundArrTime }}</div></div>
        <div class="row"><div class="k">Return</div><div class="v">{{ block.trip.returnDate }} {{ block.trip.returnDepTime }} ({{ block.trip.returnFlight }}) → {{ block.trip.returnArrTime }}</div></div>
        <div class="row"><div class="k">Price</div><div class="v">{{ block.trip.price_text or '-' }}</div></div>

        {% if block.images and block.images|length > 0 %}
        <div class="images">
          {% for im in block.images %}
          <img src="{{ im.url }}" alt="image" loading="lazy" />
          {% endfor %}
        </div>
        {% endif %}

        {% if block.amenities and block.amenities|length > 0 %}
        <div class="amenities">
          <strong>Amenities:</strong>
          {{ block.amenities | map(attribute='name') | list | join(', ') }}
        </div>
        {% endif %}

        {% if block.reviews and block.reviews|length > 0 %}
        <div class="reviews">
          <strong>Reviews:</strong>
          {% for r in block.reviews %}
            <div class="review">★ {{ r.rating or '-' }} — {{ r.text }}</div>
          {% endfor %}
        </div>
        {% endif %}
      </section>
    {% endfor %}
  </main>

  <footer>
    <div class="container">
      Generated by Sirev Proposal Generator — {{ base_url }}
    </div>
  </footer>
</body>
</html>
