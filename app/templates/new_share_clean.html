<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Client Trip Proposal · {{ company_name }}</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Playfair+Display:wght@600;700&display=swap" rel="stylesheet">

  <!-- PhotoSwipe v5 for image zoom/lightbox -->
  <link rel="stylesheet" href="https://unpkg.com/photoswipe@5/dist/photoswipe.css">

  <!-- Main stylesheet -->
  <link rel="stylesheet" href="/static/css/share.css">
</head>
<body>
  <!-- Brand header -->
  <div class="brand-bar" role="banner">
    <div class="brand-inner">
      <div>
        <div class="brand-name"><h2>{{ company_name }}</h2></div>
        <div class="brand-tag">{{ company_tagline }}</div>
      </div>
      <div class="brand-contact">
        <div class="line">
          <span class="item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path d="M12 2a9 9 0 1 0 9 9a9.01 9.01 0 0 0-9-9Zm0 2c.62 0 1.22.08 1.79.23A13 13 0 0 0 12 11H4.06A7 7 0 0 1 12 4Zm-7.94 9H11a13 13 0 0 0 1.79 6.77A7 7 0 0 1 4.06 13ZM13 20a11 11 0 0 1-2 0A15 15 0 0 1 9 13h6a15 15 0 0 1-2 7Zm2.15-1.23A13 13 0 0 0 20 13h0a7 7 0 0 1-4.85 5.77ZM19.94 11H14a13 13 0 0 0-1.79-6.77A7 7 0 0 1 19.94 11Z"/>
            </svg> 
            <span>{{ company_address }}</span>
          </span>
          <span class="item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path d="M6.62 10.79a15.05 15.05 0 0 0 6.59 6.59l2.2-2.2a1 1 0 0 1 1.01-.24a11.36 11.36 0 0 0 3.56.57a1 1 0 0 1 1 1V20a1 1 0 0 1-1 1A17 17 0 0 1 3 7a1 1 0 0 1 1-1h2.49a1 1 0 0 1 1 1a11.36 11.36 0 0 0 .57 3.56a1 1 0 0 1-.24 1.01Z"/>
            </svg> 
            <a href="tel:{{ company_phone }}">{{ company_phone }}</a>
          </span>
          <span class="item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path d="M20 4H4a2 2 0 0 0-2 2v.35l10 6.25l10-6.25V6a2 2 0 0 0-2-2Zm0 4.2l-8 5L4 8.2V18a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2Z"/>
            </svg> 
            <a href="mailto:{{ company_email }}">{{ company_email }}</a>
          </span>
        </div>
        <div class="brand-social">
          <a href="{{ company_website }}" target="_blank" rel="noopener">
            <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2Zm0 2a8 8 0 0 1 2.65.45A13 13 0 0 0 12 11H5.06A8 8 0 0 1 12 4Zm-6.94 9H12a13 13 0 0 0 2.65 6.55A8 8 0 0 1 5.06 13ZM14.3 20.55A15 15 0 0 1 13 13h5.94a8 8 0 0 1-4.64 7.55ZM18.94 11H13a15 15 0 0 1 1.3-7.55A8 8 0 0 1 18.94 11Z"/>
            </svg> 
            Website
          </a>
          <a href="{{ company_facebook }}" target="_blank" rel="noopener">
            <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path d="M13 22V12h3l1-4h-4V6c0-1.1.9-2 2-2h2V0h-3a5 5 0 0 0-5 5v3H6v4h3v10z"/>
            </svg> 
            Facebook
          </a>
          <a href="{{ company_instagram }}" target="_blank" rel="noopener">
            <svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
              <path d="M7 2h10a5 5 0 0 1 5 5v10a5 5 0 0 1-5 5H7a5 5 0 0 1-5-5V7a5 5 0 0 1 5-5Zm0 2a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3H7Zm5 3.5A5.5 5.5 0 1 1 6.5 13A5.5 5.5 0 0 1 12 7.5Zm0 2A3.5 3.5 0 1 0 15.5 13A3.5 3.5 0 0 0 12 9.5ZM18 6.5a1 1 0 1 1-1 1a1 1 0 0 1 1-1Z"/>
            </svg> 
            Instagram
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Full-page cover intro -->
  <section class="intro page-break" aria-label="Proposal Cover">
    <div class="intro-card container">
      <div class="intro-eyebrow">{{ destination }} · {{ travel_dates }}</div>
      <h1 class="intro-title">
        Time to Plan Your Dream Vacation
      </h1>
      <p class="intro-sub">
      </p>
      <div class="cta-row">
        <a class="btn" href="#itinerary">View Itinerary</a>
        <a class="btn" href="tel:{{ company_phone }}">Contact Your Agent</a>
      </div>
    </div>
  </section>

  <!-- ===== GENERAL INFO ===== -->
  <section class="general-info page-break" aria-label="General information">
    <div class="info-card container">
      <div class="start-info">
          <p>
              <strong>Thank you for choosing us to organize your trip!</strong>
In this file, you will find hotels selected according to your preferences.
All prices are listed in Canadian dollars and include taxes.
Please note that the prices are valid at the time this file was created and may change.
          </p>
      </div>
      <h3 class="what-included-title">What's Included &amp; Not Included</h3>
      <p class="small" style="margin-top:4px;"></p>
      <div class="grid" style="margin-top:16px;">
        <div>
          <h4 class="section-title">Included</h4>
          <ul class="info-list">
            <li><span class="badge-check">✔</span> Round-trip flights</li>
            <li><span class="badge-check">✔</span> Hotel accommodation and meals</li>
            <li><span class="badge-check">✔</span> Transfers airport -hotel - airport</li>
            <li><span class="badge-check">✔</span> Taxes &amp; surcharges as of today</li>
          </ul>
        </div>
        <div>
          <h4 class="section-title">Not Included</h4>
          <ul class="info-list">
            <li><span class="badge-x">✖</span> Travel insurance</li>
            <li><span class="badge-x">✖</span> Excursions</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <main id="itinerary" class="container" role="main">
    <!-- Hotels will be dynamically populated here -->
    {% for block in blocks %}
    <section class="hotel page-break" aria-labelledby="hotel-{{ loop.index }}-title">
      <div class="hotel-card">
        <!-- Gallery -->
        <div class="gallery layout-5" data-pswp-gallery="hotel-{{ loop.index }}-gallery">
          {% if block.hotel.images %}
            {% for image in block.hotel.images[:5] %}
            <a href="{{ image.url }}" data-pswp-width="{{ image.width or 1600 }}" data-pswp-height="{{ image.height or 1067 }}" 
               class="{% if loop.first %}g-main{% else %}g-{{ loop.index0 }}{% endif %}" 
               aria-label="Open image {{ loop.index }} of {{ block.hotel.name }}">
              <img src="{{ image.thumbnail_url or image.url }}" alt="{{ block.hotel.name }} — {{ image.alt or 'Hotel image' }}" loading="lazy">
            </a>
            {% endfor %}
          {% else %}
            <!-- Placeholder images if no hotel images available -->
            <a href="/static/images/placeholder-hotel.svg" data-pswp-width="1600" data-pswp-height="1067" class="g-main">
              <img src="/static/images/placeholder-hotel.svg" alt="{{ block.hotel.name }} — Main view" loading="lazy">
            </a>
          {% endif %}
        </div>

        <!-- See all photos and video tour links -->
        <div class="media-links">
          {% if block.hotel.see_all_photos %}
          <a href="{{ block.hotel.see_all_photos }}" target="_blank" rel="noopener" class="photos-link">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <polyline points="21,15 16,10 5,21"/>
            </svg>
            See all photos
          </a>
          {% endif %}

          {% if block.hotel.video_link %}
          <button class="video-tour-btn" onclick="openVideoModal('{{ block.hotel.video_link }}')">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
            Virtual Tour
          </button>
          {% endif %}
        </div>

        <!-- Hotel header & meta -->
        <header class="hotel-header">
          <h2 id="hotel-{{ loop.index }}-title">{{ block.hotel.name }}</h2>
          <div class="meta-row">
            <span class="meta" aria-label="Location">
              <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true">
                <path fill="currentColor" d="M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7Zm0 9.5a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5Z"/>
              </svg>
              {{ block.hotel.city }}{% if block.hotel.country %}, {{ block.hotel.country }}{% endif %}
            </span>
            {% if block.hotel.rating %}
            <span class="meta" aria-label="Rating">
              <span class="rating">
                <span class="stars" aria-hidden="true">
                  {% for i in range(5) %}
                  <svg class="star" viewBox="0 0 24 24" {% if i >= block.hotel.rating %}style="opacity:.35"{% endif %}>
                    <path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/>
                  </svg>
                  {% endfor %}
                </span>
                <span aria-label="{{ block.hotel.rating }} out of 5">{{ block.hotel.rating }}</span>/5
                {% if block.hotel.num_reviews %}
                <span class="review-count">({{ block.hotel.num_reviews }} reviews)</span>
                {% endif %}
                {% if block.hotel.web_url %}
                <a href="{{ block.hotel.web_url }}#REVIEWS" target="_blank" rel="noopener" class="review-link">See all reviews</a>
                {% endif %}
              </span>
            </span>
            {% endif %}
          </div>

          <!-- Comment from agency -->
          {% if block.trip.comment %}
          <div class="agency-comment">
            <h4>Наш коментар</h4>
            <p>{{ block.trip.comment }}</p>
          </div>
          {% endif %}

          <!-- Amenities -->
          {% if block.hotel.amenities %}
          <ul class="amenities" aria-label="Main amenities">
            {% for amenity in block.hotel.amenities[:5] %}
            <li>{{ amenity.name }}</li>
            {% endfor %}
          </ul>
          {% if block.hotel.amenities|length > 5 %}
          <ul class="amenities collapsed" aria-label="All amenities">
            {% for amenity in block.hotel.amenities %}
            <li data-cat="{{ amenity.category or 'general' }}">{{ amenity.name }}</li>
            {% endfor %}
          </ul>
          <button class="amenities-toggle" type="button" aria-expanded="false">Show all amenities</button>
          {% endif %}
          {% endif %}
        </header>

        <!-- Details -->
        <div class="details">
          <!-- Trip summary -->
          <section class="panel" aria-label="Trip information">
            <div class="trip-table">
              <div class="trip-cell"><div class="label">Nights</div><div class="value">{{ block.trip.nights or 'N/A' }}</div></div>
              <div class="trip-cell"><div class="label">Travel Dates</div><div class="value">{{ block.trip.outbound_date }} – {{ block.trip.return_date }}</div></div>
              {% if block.trip.room_desc %}
              <div class="trip-cell"><div class="label">Room Type</div><div class="value">{{ block.trip.room_desc | safe }}</div></div>
              {% endif %}
              {% if block.trip.tour_operator %}
              <div class="trip-cell"><div class="label">Tour Operator</div><div class="value">{{ block.trip.tour_operator | convert_tour_operator }}</div></div>
              {% endif %}
             <div class="trip-cell price2">
               <div class="label">Price</div>
               <div class="value">
                 {% if block.trip.full_price and block.trip.price and block.trip.full_price != block.trip.price %}
                   <span class="original-price" style="text-decoration: line-through; color: #666;">${{ "{:,.0f}".format(block.trip.full_price) }} CAD</span>
                   <br>
                   <span class="discounted-price" style="color: #d32f2f; font-weight: bold;">${{ "{:,.0f}".format(block.trip.price) }} CAD</span>
                 {% elif block.trip.price %}
                   ${{ "{:,.0f}".format(block.trip.price) }} CAD
                 {% else %}
                   Contact for pricing
                 {% endif %}
                 <span class="small">{{ block.trip.people_per_trip | lower if block.trip.people_per_trip else '2 people + child' }}</span>
               </div>
             </div>

{#              <div class="trip-cell price"><div class="label">Price</div><div class="value">{{ block.trip.price_text or 'Contact for pricing' }} <span class="small">2 people + child</span></div></div>#}
            </div>
          </section>

          <!-- Flights -->
          {% if block.trip.outbound_flight or block.trip.return_flight %}
          <section class="panel" aria-label="Flight details">
            <div class="section-title">Flights</div>
            <div class="flights two-col">
              <!-- Outbound -->
              {% if block.trip.outbound_flight %}
              <div class="flight" aria-label="Outbound flight">
                <div class="route">
                  <svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true">
                    <path fill="currentColor" d="M21 16v-2l-8-5V3.5a1.5 1.5 0 0 0-3 0V9L2 13v2l8-2.5V19l-2 1.5V22l3-1l3 1v-1.5L13 19v-8.5z"/>
                  </svg> 
                  Departure
                </div>
                <div class="time-row">
                  <div><strong>Depart</strong> {{ block.trip.outbound_date }} · {{ block.trip.outbound_dep_time }}</div>
                  <div><strong>Arrive</strong> {{ block.trip.outbound_date }} · {{ block.trip.outbound_arr_time }}</div>
                  <div><strong>Flight</strong> {{ block.trip.outbound_flight }}</div>
                </div>
              </div>
              {% endif %}
              
              <!-- Return -->
              {% if block.trip.return_flight %}
              <div class="flight" aria-label="Return flight">
                <div class="route">
                  <svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true">
                    <path fill="currentColor" d="M21 16v-2l-8-5V3.5a1.5 1.5 0 0 0-3 0V9L2 13v2l8-2.5V19l-2 1.5V22l3-1l3 1v-1.5L13 19v-8.5z"/>
                  </svg> 
                  Return
                </div>
                <div class="time-row">
                  <div><strong>Depart</strong> {{ block.trip.return_date }} · {{ block.trip.return_dep_time }}</div>
                  <div><strong>Arrive</strong> {{ block.trip.return_date }} · {{ block.trip.return_arr_time }}</div>
                  <div><strong>Flight</strong> {{ block.trip.return_flight }}</div>
                </div>
              </div>
              {% endif %}
            </div>
          </section>
          {% endif %}
        </div>
      </div>
    </section>
    {% endfor %}
  </main>

  <!-- ===== THANK YOU ===== -->
{#  <footer class="thanks" id="contact">#}
{#    <p>Thank you for choosing {{ company_name }}. We're honored to plan this journey and can't wait to bring it to life. If you have any questions please contact us.</p>#}
{#  </footer>#}

  <!-- Video Modal -->
  <div id="videoModal" class="video-modal" onclick="closeVideoModal()">
    <div class="video-modal-content" onclick="event.stopPropagation()">
      <button class="video-modal-close" onclick="closeVideoModal()">&times;</button>
      <div class="video-modal-container">
        <div id="videoContainer"></div>
      </div>
    </div>
  </div>

  <!-- PhotoSwipe: Minimal lightbox initialization -->
  <script type="module">
    import PhotoSwipeLightbox from 'https://unpkg.com/photoswipe@5/dist/photoswipe-lightbox.esm.js';

    const lightbox = new PhotoSwipeLightbox({
      gallery: '.gallery',
      children: 'a',
      pswpModule: () => import('https://unpkg.com/photoswipe@5/dist/photoswipe.esm.js')
    });
    lightbox.init();
  </script>

  <!-- Main JavaScript -->
  <script src="/static/js/share.js"></script>
</body>
</html>
