<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Client Trip Proposal · {{Company Name}}</title>

  <!-- Google Fonts (safe fallbacks provided) -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Playfair+Display:wght@600;700&display=swap" rel="stylesheet">

  <!-- PhotoSwipe v5 for image zoom/lightbox -->
  <link rel="stylesheet" href="https://unpkg.com/photoswipe@5/dist/photoswipe.css">

  <style>
    :root {
      --brand: #d62828;       /* primary red */
      --brand-weak: #ffe5e5;  /* subtle red tint */
      --text: #1f2937;        /* slate-800 */
      --muted: #6b7280;       /* slate-500 */
      --ink: #111827;         /* slate-900 */
      --bg: #ffffff;
      --card: #ffffff;
      --shadow: 0 6px 24px rgba(0,0,0,.08), 0 2px 6px rgba(0,0,0,.06);
      --radius: 18px;
      --maxw: 1100px;
    }

    /* ----- Global Resets & Type ----- */
    *, *::before, *::after { box-sizing: border-box; }
    html, body { height: 100%; }
    body {
      margin: 0;
      font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
      color: var(--text);
      background: var(--bg);
      line-height: 1.55;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    h1, h2, h3 { font-family: "Playfair Display", Georgia, Cambria, Cochin, serif; color: var(--ink); margin: 0 0 .4rem; line-height: 1.15; }
    h1 { font-size: clamp(36px, 6vw, 64px); letter-spacing: -0.02em; }
    h2 { font-size: clamp(24px, 3.4vw, 36px); }
    p { margin: 0 0 1rem; }
    a { color: inherit; text-decoration: none; }

    .container { width: min(100%, var(--maxw)); margin-inline: auto; padding: 24px; }

    /* ----- Brand Header Bar (Sticky + contact & socials) ----- */
    .brand-bar {
      position: sticky; top: 0; z-index: 999;
      background: var(--brand);
      color: white;
      padding: 14px 24px;
      box-shadow: var(--shadow);
    }
    .brand-inner { width: min(100%, var(--maxw)); margin: 0 auto; display: flex; align-items: center; justify-content: space-between; gap: 16px; }
    .brand-name { font-weight: 700; letter-spacing: .5px; font-size: 20px; text-transform: uppercase; }
    .brand-tag { opacity: .9; font-size: 14px; }

    .brand-contact { display: flex; flex-direction: column; align-items: flex-end; gap: 6px; font-size: 13px; }
    .brand-contact .line { display: flex; gap: 14px; flex-wrap: wrap; justify-content: flex-end; }
    .brand-contact a { color: #fff; text-decoration: underline; text-decoration-color: rgba(255,255,255,.5); }
    .brand-contact .item { display: inline-flex; align-items: center; gap: 6px; opacity: .95; }

    .brand-social { display: flex; gap: 12px; flex-wrap: wrap; }
    .brand-social a { display: inline-flex; align-items: center; gap: 6px; font-size: 13px; opacity: .95; }
    .brand-social svg { width: 16px; height: 16px; }

    @media (max-width: 700px){
      .brand-inner { flex-direction: column; align-items: flex-start; gap: 8px; }
      .brand-contact { align-items: flex-start; }
      .brand-contact .line { justify-content: flex-start; }
    }

    /* ----- Full-page Intro (Hero) ----- */
    .intro {
      min-height: 100svh; /* better on mobile */
      display: grid; place-items: center; position: relative;
      isolation: isolate; overflow: hidden; color: white;
    }
    .intro::before {
      content: "";
      position: absolute; inset: 0;
      background: url("images/hero-placeholder.jpg") center/cover no-repeat; /* Replace with your full-bleed image */
      transform: scale(1.02);
      filter: brightness(.65) saturate(1.05);
      z-index: -2;
    }
    .intro::after {
      content: "";
      position: absolute; inset: 0;
      background: linear-gradient(to top, rgba(0,0,0,.55), rgba(0,0,0,.15));
      z-index: -1;
    }
    .intro-card {
      text-align: center; padding: clamp(24px, 4vw, 48px);
      backdrop-filter: blur(2px);
    }
    .intro-eyebrow {
      display: inline-block; background: rgba(255,255,255,.12); border: 1px solid rgba(255,255,255,.25);
      padding: 6px 12px; border-radius: 999px; margin-bottom: 14px; font-weight: 600; letter-spacing: .06em;
    }
    .intro h1 span { color: #fff; }
    .intro-sub { max-width: 760px; margin-inline: auto; font-size: clamp(16px, 1.8vw, 20px); opacity: .95; }

    .cta-row { margin-top: 24px; display: flex; gap: 12px; justify-content: center; flex-wrap: wrap; }
    .btn {
      display: inline-flex; align-items: center; gap: 10px; padding: 12px 18px; border-radius: 999px;
      border: 1px solid rgba(255,255,255,.35); background: rgba(255,255,255,.08); color: #fff; font-weight: 600;
      backdrop-filter: blur(4px);
    }

    /* Print hint to start a new page after the cover */
    .page-break { break-after: page; }

    /* ----- General Info Block ----- */
    .general-info { padding: 18px 0 8px; }
    .info-card { background: var(--card); border-radius: var(--radius); box-shadow: var(--shadow); border: 1px solid #eef1f4; }
    .general-info h2 { margin-bottom: 6px; }
    .general-info .grid { display: grid; gap: 18px; grid-template-columns: 1fr 1fr; }
    @media (max-width: 900px){ .general-info .grid { grid-template-columns: 1fr; } }
    .info-list { margin: 0; padding: 0; display: grid; gap: 8px; }
    .info-list li { list-style: none; display: flex; gap: 10px; align-items: flex-start; }
    .badge-check { color: #059669; font-weight: 700; }
    .badge-x { color: var(--muted); font-weight: 700; }

    /* ----- Hotel Section ----- */
    section.hotel { padding: 28px 0 40px; }
    .hotel-card { background: var(--card); border-radius: var(--radius); box-shadow: var(--shadow); overflow: hidden; }
    .hotel-header { padding: clamp(18px, 2vw, 24px); border-bottom: 1px solid #eef1f4; display: grid; gap: 8px; }

    .meta-row { display: flex; flex-wrap: wrap; gap: 14px 18px; align-items: center; color: var(--muted); font-size: 14px; }
    .meta { display: inline-flex; align-items: center; gap: 8px; }
    .dot { width: 6px; height: 6px; border-radius: 999px; background: var(--brand); display: inline-block; }
    $1

    .rating { display: inline-flex; align-items: center; gap: 8px; font-weight: 700; }
    .stars { display: inline-flex; gap: 2px; }
    .star { width: 18px; height: 18px; fill: #f59e0b; display: block; }

    /* ----- Amenities (simple pills) ----- */
    .amenities { display: flex; flex-wrap: wrap; gap: 8px; margin-top: 4px; }
    .amenities li { list-style: none; padding: 6px 10px; border-radius: 999px; background: var(--brand-weak); color: var(--ink); font-size: 13px; }

    /* ----- 5-up Gallery (click to zoom via PhotoSwipe) ----- */
    .gallery { display: grid; gap: 8px; padding: 8px; background: #fafafa; }
    /* Desktop collage of five using grid-areas */
    .gallery.layout-5 {
      grid-template-columns: 2fr 1fr 1fr; /* three columns */
      grid-template-rows: 220px 160px;     /* two rows */
      grid-template-areas:
        "main side1 side2"
        "main side3 side4";
    }
    @media (min-width: 980px){
      .gallery.layout-5 { grid-template-rows: 320px 240px; }
    }
    @media (max-width: 700px){
      .gallery.layout-5 { grid-template-columns: 1fr 1fr; grid-template-rows: repeat(3, 160px); grid-template-areas:
        "main main"
        "side1 side2"
        "side3 side4"; }
    }

    .gallery a { position: relative; border-radius: 14px; overflow: hidden; display: block; }
    .gallery a img { width: 100%; height: 100%; object-fit: cover; display: block; transition: transform .35s ease; }
    .gallery a:hover img { transform: scale(1.03); }

    .gallery .g-main { grid-area: main; }
    .gallery .g-1 { grid-area: side1; }
    .gallery .g-2 { grid-area: side2; }
    .gallery .g-3 { grid-area: side3; }
    .gallery .g-4 { grid-area: side4; }

    /* ----- Horizontal Trip Info (compact) ----- */
    .panel { background: #fcfcfd; border: 1px solid #eef1f4; border-radius: 14px; padding: 14px 16px; }
    .section-title { font-weight: 700; letter-spacing: .2px; margin-bottom: 8px; }

    .trip-table { display: grid; grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); gap: 10px; }
    .trip-cell { background: #fff; border: 1px solid #eef1f4; border-radius: 12px; padding: 10px 12px; }
    .trip-cell .label { color: var(--muted); font-size: 12px; text-transform: uppercase; letter-spacing: .04em; }
    .trip-cell .value { font-weight: 700; margin-top: 4px; }
    .trip-cell.price .value { font-size: 22px; color: var(--ink); }

    /* ----- Flights: two-column layout ----- */
    .flights { display: grid; gap: 12px; }
    .flights.two-col { grid-template-columns: 1fr 1fr; }
    @media (max-width: 900px){ .flights.two-col { grid-template-columns: 1fr; } }
    .flight { border: 1px dashed #e5e7eb; border-radius: 12px; padding: 12px; display: grid; gap: 8px; }
    .route { font-weight: 700; display: flex; align-items: center; gap: 8px; }
    .time-row { display: flex; flex-wrap: wrap; gap: 10px 16px; color: var(--muted); font-size: 14px; }

    footer.thanks { text-align: center; color: var(--muted); padding: 28px 0 60px; }

    /* ----- Print Styles ----- */
    @media print {
      .brand-bar { position: fixed; top: 0; left: 0; right: 0; box-shadow: none; }
      body { margin-top: 72px; }
      .btn { display: none !important; }
      .intro { min-height: auto; height: 100vh; }
      .gallery { break-inside: avoid; }
      .hotel-card { box-shadow: none; border: 1px solid #ccc; }
      .page-break { break-after: page; }
    }
  </style>
</head>
<body>
  <!-- Brand header -->
  <div class="brand-bar" role="banner">
    <div class="brand-inner">
      <div>
        <div class="brand-name">{{Company Name}}</div>
        <div class="brand-tag">Tailored Travel Proposal</div>
      </div>
      <div class="brand-contact">
        <div class="line">
          <span class="item"><svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M12 2a9 9 0 1 0 9 9a9.01 9.01 0 0 0-9-9Zm0 2c.62 0 1.22.08 1.79.23A13 13 0 0 0 12 11H4.06A7 7 0 0 1 12 4Zm-7.94 9H11a13 13 0 0 0 1.79 6.77A7 7 0 0 1 4.06 13ZM13 20a11 11 0 0 1-2 0A15 15 0 0 1 9 13h6a15 15 0 0 1-2 7Zm2.15-1.23A13 13 0 0 0 20 13h0a7 7 0 0 1-4.85 5.77ZM19.94 11H14a13 13 0 0 0-1.79-6.77A7 7 0 0 1 19.94 11Z"/></svg> <span>{{123 Main St, City, Country}}</span></span>
          <span class="item"><svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M6.62 10.79a15.05 15.05 0 0 0 6.59 6.59l2.2-2.2a1 1 0 0 1 1.01-.24a11.36 11.36 0 0 0 3.56.57a1 1 0 0 1 1 1V20a1 1 0 0 1-1 1A17 17 0 0 1 3 7a1 1 0 0 1 1-1h2.49a1 1 0 0 1 1 1a11.36 11.36 0 0 0 .57 3.56a1 1 0 0 1-.24 1.01Z"/></svg> <a href="tel:{{******-123-4567}}">{{****** 123 4567}}</a></span>
          <span class="item"><svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M20 4H4a2 2 0 0 0-2 2v.35l10 6.25l10-6.25V6a2 2 0 0 0-2-2Zm0 4.2l-8 5L4 8.2V18a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2Z"/></svg> <a href="mailto:{{<EMAIL>}}">{{<EMAIL>}}</a></span>
        </div>
        <div class="brand-social">
          <a href="{{https://agencywebsite.com}}" target="_blank" rel="noopener"><svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2Zm0 2a8 8 0 0 1 2.65.45A13 13 0 0 0 12 11H5.06A8 8 0 0 1 12 4Zm-6.94 9H12a13 13 0 0 0 2.65 6.55A8 8 0 0 1 5.06 13ZM14.3 20.55A15 15 0 0 1 13 13h5.94a8 8 0 0 1-4.64 7.55ZM18.94 11H13a15 15 0 0 1 1.3-7.55A8 8 0 0 1 18.94 11Z"/></svg> Website</a>
          <a href="{{https://facebook.com/agency}}" target="_blank" rel="noopener"><svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M13 22V12h3l1-4h-4V6c0-1.1.9-2 2-2h2V0h-3a5 5 0 0 0-5 5v3H6v4h3v10z"/></svg> Facebook</a>
          <a href="{{https://instagram.com/agency}}" target="_blank" rel="noopener"><svg viewBox="0 0 24 24" fill="currentColor" aria-hidden="true"><path d="M7 2h10a5 5 0 0 1 5 5v10a5 5 0 0 1-5 5H7a5 5 0 0 1-5-5V7a5 5 0 0 1 5-5Zm0 2a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3H7Zm5 3.5A5.5 5.5 0 1 1 6.5 13A5.5 5.5 0 0 1 12 7.5Zm0 2A3.5 3.5 0 1 0 15.5 13A3.5 3.5 0 0 0 12 9.5ZM18 6.5a1 1 0 1 1-1 1a1 1 0 0 1 1-1Z"/></svg> Instagram</a>
        </div>
      </div>
    </div>
  </div>

  <!-- Full-page cover intro -->
  <section class="intro page-break" aria-label="Proposal Cover">
    <div class="intro-card container">
      <div class="intro-eyebrow">{{Destination}} · {{Dates}}</div>
      <h1>
        Your Dream Trip to <span>{{Destination}}</span>
      </h1>
      <p class="intro-sub">
        Curated by {{Agent Name}} for {{Client Name}} — a handcrafted journey featuring exceptional stays, thoughtful routes, and seamless flights. Swap the stress for sunshine; we’ll handle the details.
      </p>
      <div class="cta-row">
        <a class="btn" href="#itinerary">View Itinerary</a>
        <a class="btn" href="#contact">Contact Your Agent</a>
      </div>
    </div>
  </section>

  <!-- ===== GENERAL INFO ===== -->
  <section class="general-info page-break" aria-label="General information">
    <div class="info-card container">
      <h2>What's Included &amp; Not Included</h2>
      <p class="small" style="margin-top:4px;">Summary of common inclusions for this proposal (customize as needed).</p>
      <div class="grid" style="margin-top:16px;">
        <div>
          <h3 class="section-title">Included</h3>
          <ul class="info-list">
            <li><span class="badge-check">✔</span> {{Hotel accommodation as described}}</li>
            <li><span class="badge-check">✔</span> {{Quoted meals/board plan}}</li>
            <li><span class="badge-check">✔</span> {{Round-trip flights (economy)}}</li>
            <li><span class="badge-check">✔</span> {{Airport transfers where stated}}</li>
            <li><span class="badge-check">✔</span> {{Taxes &amp; surcharges as of {{Today}}}}</li>
          </ul>
        </div>
        <div>
          <h3 class="section-title">Not Included</h3>
          <ul class="info-list">
            <li><span class="badge-x">✖</span> {{Travel insurance}}</li>
            <li><span class="badge-x">✖</span> {{Checked luggage unless specified}}</li>
            <li><span class="badge-x">✖</span> {{Visa fees &amp; entry requirements}}</li>
            <li><span class="badge-x">✖</span> {{Resort fees / local taxes payable on site}}</li>
            <li><span class="badge-x">✖</span> {{Personal expenses, tips, optional tours}}</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <main id="itinerary" class="container" role="main">

    <!-- ===== HOTEL 1 ===== -->
    <section class="hotel page-break" aria-labelledby="hotel-1-title">
      <div class="hotel-card">
        <!-- 5-up gallery -->
        <div class="gallery layout-5" data-pswp-gallery="hotel-1-gallery">
          <!-- Replace src/href + sizes with your images. Width/height should reflect the LARGE image for smooth zoom. -->
          <a href="images/hotel1-1.jpg" data-pswp-width="1600" data-pswp-height="1067" class="g-main" aria-label="Open image 1 of hotel 1">
            <img src="images/hotel1-1_thumb.jpg" alt="Hotel 1 — Main view" loading="lazy">
          </a>
          <a href="images/hotel1-2.jpg" data-pswp-width="1200" data-pswp-height="1500" class="g-1" aria-label="Open image 2 of hotel 1">
            <img src="images/hotel1-2_thumb.jpg" alt="Hotel 1 — Room" loading="lazy">
          </a>
          <a href="images/hotel1-3.jpg" data-pswp-width="1600" data-pswp-height="1067" class="g-2" aria-label="Open image 3 of hotel 1">
            <img src="images/hotel1-3_thumb.jpg" alt="Hotel 1 — Pool" loading="lazy">
          </a>
          <a href="images/hotel1-4.jpg" data-pswp-width="1600" data-pswp-height="1067" class="g-3" aria-label="Open image 4 of hotel 1">
            <img src="images/hotel1-4_thumb.jpg" alt="Hotel 1 — Restaurant" loading="lazy">
          </a>
          <a href="images/hotel1-5.jpg" data-pswp-width="1200" data-pswp-height="1500" class="g-4" aria-label="Open image 5 of hotel 1">
            <img src="images/hotel1-5_thumb.jpg" alt="Hotel 1 — Lobby" loading="lazy">
          </a>
        </div>

        <!-- Hotel header & meta -->
        <header class="hotel-header">
          <h2 id="hotel-1-title">{{Hotel Name 1}}</h2>
          <div class="meta-row">
            <span class="meta" aria-label="Location"><svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7Zm0 9.5a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5Z"/></svg>{{City, Country}}</span>
            <span class="meta" aria-label="Rating"><span class="rating"><span class="stars" aria-hidden="true">
              <svg class="star" viewBox="0 0 24 24"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
              <svg class="star" viewBox="0 0 24 24"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
              <svg class="star" viewBox="0 0 24 24"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
              <svg class="star" viewBox="0 0 24 24"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
              <svg class="star" viewBox="0 0 24 24" style="opacity:.35"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
            </span> <span aria-label="4.5 out of 5">4.5</span>/5</span> <a class="reviews" href="{{Reviews URL}}" target="_blank" rel="noopener">({{1,234}} reviews)</a></span>
            <span class="meta"><span class="dot"></span> Boutique • Beachfront</span>
          </div>
          <ul class="amenities" aria-label="Main amenities">
            <li>Free Wi‑Fi</li><li>Breakfast Included</li><li>Spa Access</li><li>Ocean View</li><li>Airport Transfer</li>
          </ul>
          <ul class="amenities collapsed" aria-label="Main amenities">
            <li data-cat="stay">Free Wi‑Fi</li>
            <li data-cat="dining">Breakfast Included</li>
            <li data-cat="wellness">Spa Access</li>
            <li data-cat="stay,wellness">Ocean View</li>
            <li data-cat="practical">Airport Transfer</li>
          </ul>
          <button class="amenities-toggle" type="button" aria-expanded="false">Show all amenities</button>
        </div>
        </header>

        <!-- Details -->
        <div class="details">
          <!-- Trip summary -->
          <section class="panel" aria-label="Trip information">
            <div class="trip-table">
              <div class="trip-cell"><div class="label">Nights</div><div class="value">{{5}}</div></div>
              <div class="trip-cell price"><div class="label">Price</div><div class="value">{{$2,850}} <span class="small">per person</span></div></div>
              <div class="trip-cell"><div class="label">Travel Dates</div><div class="value">{{Oct 10 – Oct 15, 2025}}</div></div>
              <div class="trip-cell"><div class="label">Board</div><div class="value">{{Bed &amp; Breakfast}}</div></div>
              <div class="trip-cell"><div class="label">Room Type</div><div class="value">{{Deluxe Sea View}}</div></div>
              <div class="trip-cell"><div class="label">Reference</div><div class="value">{{REF-12345}}</div></div>
            </div>
          </section>

          <!-- Flights -->
          <section class="panel" aria-label="Flight details">
            <div class="section-title">Flights</div>
            <div class="flights two-col">
              <!-- Outbound (left) -->
              <div class="flight" aria-label="Outbound flight">
                <div class="route"><svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M21 16v-2l-8-5V3.5a1.5 1.5 0 0 0-3 0V9L2 13v2l8-2.5V19l-2 1.5V22l3-1l3 1v-1.5L13 19v-8.5z"/></svg> {{YYZ}} → {{MIA}}</div>
                <div class="time-row">
                  <div><strong>Flight</strong> {{AC123}}</div>
                  <div><strong>Depart</strong> {{Oct 10 · 10:20}} (YYZ)</div>
                  <div><strong>Arrive</strong> {{Oct 10 · 13:35}} (MIA)</div>
                  <div><strong>Duration</strong> {{3h 15m}}</div>
                </div>
              </div>
              <!-- Return (right) -->
              <div class="flight" aria-label="Return flight">
                <div class="route"><svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M21 16v-2l-8-5V3.5a1.5 1.5 0 0 0-3 0V9L2 13v2l8-2.5V19l-2 1.5V22l3-1l3 1v-1.5L13 19v-8.5z"/></svg> {{MIA}} → {{YYZ}}</div>
                <div class="time-row">
                  <div><strong>Flight</strong> {{AC456}}</div>
                  <div><strong>Depart</strong> {{Oct 15 · 14:30}} (MIA)</div>
                  <div><strong>Arrive</strong> {{Oct 15 · 17:45}} (YYZ)</div>
                  <div><strong>Duration</strong> {{3h 15m}}</div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>

    <!-- ===== HOTEL 2 (Duplicate & edit for more hotels) ===== -->
    <section class="hotel page-break" aria-labelledby="hotel-2-title">
      <div class="hotel-card">
        <div class="gallery layout-5" data-pswp-gallery="hotel-2-gallery">
          <a href="images/hotel2-1.jpg" data-pswp-width="1600" data-pswp-height="1067" class="g-main"><img src="images/hotel2-1_thumb.jpg" alt="Hotel 2 — Main view" loading="lazy"></a>
          <a href="images/hotel2-2.jpg" data-pswp-width="1200" data-pswp-height="1500" class="g-1"><img src="images/hotel2-2_thumb.jpg" alt="Hotel 2 — Room" loading="lazy"></a>
          <a href="images/hotel2-3.jpg" data-pswp-width="1600" data-pswp-height="1067" class="g-2"><img src="images/hotel2-3_thumb.jpg" alt="Hotel 2 — Pool" loading="lazy"></a>
          <a href="images/hotel2-4.jpg" data-pswp-width="1600" data-pswp-height="1067" class="g-3"><img src="images/hotel2-4_thumb.jpg" alt="Hotel 2 — Restaurant" loading="lazy"></a>
          <a href="images/hotel2-5.jpg" data-pswp-width="1200" data-pswp-height="1500" class="g-4"><img src="images/hotel2-5_thumb.jpg" alt="Hotel 2 — Lobby" loading="lazy"></a>
        </div>

        <header class="hotel-header">
          <h2 id="hotel-2-title">{{Hotel Name 2}}</h2>
          <div class="meta-row">
            <span class="meta" aria-label="Location"><svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M12 2a7 7 0 0 0-7 7c0 5.25 7 13 7 13s7-7.75 7-13a7 7 0 0 0-7-7Zm0 9.5a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5Z"/></svg>{{City, Country}}</span>
            <span class="meta" aria-label="Rating"><span class="rating"><span class="stars" aria-hidden="true">
              <svg class="star" viewBox="0 0 24 24"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
              <svg class="star" viewBox="0 0 24 24"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
              <svg class="star" viewBox="0 0 24 24"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
              <svg class="star" viewBox="0 0 24 24"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
              <svg class="star" viewBox="0 0 24 24" style="opacity:.35"><path d="M12 17.3l-6.18 3.73l1.64-7.03L2 9.24l7.19-.61L12 2l2.81 6.63L22 9.24l-5.46 4.76l1.64 7.03"/></svg>
            </span> <span aria-label="4.5 out of 5">4.5</span>/5</span> <a class="reviews" href="{{Reviews URL}}" target="_blank" rel="noopener">({{1,234}} reviews)</a></span>
            <span class="meta"><span class="dot"></span> Family‑friendly • City Center</span>
          </div>
          <ul class="amenities" aria-label="Main amenities">
            <li>Free Wi‑Fi</li><li>Breakfast Included</li><li>Spa Access</li><li>Ocean View</li><li>Airport Transfer</li>
          </ul>
          <ul class="amenities collapsed" aria-label="Main amenities">
            <li data-cat="stay">Free Wi‑Fi</li>
            <li data-cat="dining">Breakfast Included</li>
            <li data-cat="wellness">Spa Access</li>
            <li data-cat="stay,wellness">Ocean View</li>
            <li data-cat="practical">Airport Transfer</li>
          </ul>
          <button class="amenities-toggle" type="button" aria-expanded="false">Show all amenities</button>
        </div>
        </header>

        <div class="details">
          <section class="panel" aria-label="Trip information">
            <div class="trip-table">
              <div class="trip-cell"><div class="label">Nights</div><div class="value">{{5}}</div></div>
              <div class="trip-cell price"><div class="label">Price</div><div class="value">{{$2,850}} <span class="small">per person</span></div></div>
              <div class="trip-cell"><div class="label">Travel Dates</div><div class="value">{{Oct 10 – Oct 15, 2025}}</div></div>
              <div class="trip-cell"><div class="label">Board</div><div class="value">{{Bed &amp; Breakfast}}</div></div>
              <div class="trip-cell"><div class="label">Room Type</div><div class="value">{{Deluxe Sea View}}</div></div>
              <div class="trip-cell"><div class="label">Reference</div><div class="value">{{REF-12345}}</div></div>
            </div>
          </section>

          <section class="panel" aria-label="Flight details">
            <div class="section-title">Flights</div>
            <div class="flights two-col">
              <!-- Outbound (left) -->
              <div class="flight" aria-label="Outbound flight">
                <div class="route"><svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M21 16v-2l-8-5V3.5a1.5 1.5 0 0 0-3 0V9L2 13v2l8-2.5V19l-2 1.5V22l3-1l3 1v-1.5L13 19v-8.5z"/></svg> {{YYZ}} → {{MIA}}</div>
                <div class="time-row">
                  <div><strong>Flight</strong> {{AC123}}</div>
                  <div><strong>Depart</strong> {{Oct 10 · 10:20}} (YYZ)</div>
                  <div><strong>Arrive</strong> {{Oct 10 · 13:35}} (MIA)</div>
                  <div><strong>Duration</strong> {{3h 15m}}</div>
                </div>
              </div>
              <!-- Return (right) -->
              <div class="flight" aria-label="Return flight">
                <div class="route"><svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true"><path fill="currentColor" d="M21 16v-2l-8-5V3.5a1.5 1.5 0 0 0-3 0V9L2 13v2l8-2.5V19l-2 1.5V22l3-1l3 1v-1.5L13 19v-8.5z"/></svg> {{MIA}} → {{YYZ}}</div>
                <div class="time-row">
                  <div><strong>Flight</strong> {{AC456}}</div>
                  <div><strong>Depart</strong> {{Oct 15 · 14:30}} (MIA)</div>
                  <div><strong>Arrive</strong> {{Oct 15 · 17:45}} (YYZ)</div>
                  <div><strong>Duration</strong> {{3h 15m}}</div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>

    <!-- ===== THANK YOU ===== -->
    <footer class="thanks" id="contact">
      <p>Thank you for choosing {{Company Name}}. We’re honored to plan this journey and can’t wait to bring it to life. If you have any questions or would like to personalize further, contact {{Agent Name}} at <a href="mailto:{{<EMAIL>}}">{{<EMAIL>}}</a> or {{Agent Phone}}.</p>
    </footer>
  </main>

  <!-- PhotoSwipe: Minimal lightbox initialization -->
  <script type="module">
    import PhotoSwipeLightbox from 'https://unpkg.com/photoswipe@5/dist/photoswipe-lightbox.esm.js';

    const lightbox = new PhotoSwipeLightbox({
      gallery: '.gallery',
      children: 'a',
      pswpModule: () => import('https://unpkg.com/photoswipe@5/dist/photoswipe.esm.js')
    });
    lightbox.init();
  </script>
</body>
</html>
