# --- Server ---
APP_NAME=Sirev Proposal Generator API
ENV=dev
HOST=*************
PORT=80
LOG_LEVEL=INFO

# --- CORS ---
# Comma-separated list of allowed origins
CORS_ALLOW_ORIGINS=chrome-extension://glecjnlgconheeinilkfeojkfafdflpd,https://www.sirev.com,http://localhost:8000,http://127.0.0.1:8000,https://*************,http://*************

# --- Database ---
DATABASE_URL=postgresql+asyncpg://toptcfjg_sirev_ext_user:nQB$+GW3$DW,@127.0.0.1:5432/toptcfjg_sirev_extension

# --- Auth ---
GOOGLE_CLIENT_ID=456832608708-7g4m3l80bf4eujjpdqveg5efge75pkhh.apps.googleusercontent.com
JWT_SECRET=DSJFKkjsbj623sdfndsjkbHVSJDK76823DKFfdsk32$82_kbf
JWT_REFRESH_SECRET=cxvnnxckj$_!wqweiOIWEHC32987983221OSDFuywqriede
JWT_ISSUER=sirev-proposal
JWT_AUDIENCE=sirev-proposal-users
ACCESS_TOKEN_EXPIRES_SECONDS=2592000
REFRESH_TOKEN_EXPIRES_SECONDS=2592000

# Dev bypass (ONLY in dev): if set to true, /auth/google/verify accepts X-Dev-Email
DEV_AUTH_BYPASS=true

# --- Enrichment ---
ENRICH_PROVIDER=mock
TRIPADVISOR_BASE_URL=https://api.content.tripadvisor.com
TRIPADVISOR_API_KEY=A21A0C3E90C34992A3C59B4D1382C2BE
TRIPADVISOR_TIMEOUT_SECONDS=8

ENRICH_CONCURRENCY=6
ENRICH_PER_CALL_TIMEOUT_SECONDS=5
ENRICH_GLOBAL_TIMEOUT_SECONDS=25

# Company Information
COMPANY_NAME=Top Travel Agency
COMPANY_TAGLINE=Tailored Trip Proposal
COMPANY_ADDRESS=1003 - 55 York Street, Toronto, ON M5J 1R7
COMPANY_PHONE=+****************
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=https://www.toptravelagency.ca/
COMPANY_FACEBOOK=https://www.facebook.com/toptravelagency.ca
COMPANY_INSTAGRAM=https://www.instagram.com/toptravelagency.ca/

# Agent Information (can be overridden per proposal)
DEFAULT_AGENT_NAME=Travel Specialist
DEFAULT_AGENT_EMAIL=<EMAIL>
DEFAULT_AGENT_PHONE=****** 123 4567
