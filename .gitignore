# Sirev Project - Git Ignore File

# Environment files
.env
.env.local
.env.production
.env.staging
*.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Database
*.db
*.sqlite3
*.sqlite

# Logs
logs/
*.log
log/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Backup files
*.bak
*.backup

# Node.js (for any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.dockerignore

# Alembic
alembic/versions/*.pyc

# Static files (if generated)
staticfiles/
static_collected/

# Media files
media/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Certificates
*.pem
*.key
*.crt
*.csr

# Secrets
secrets/
*.secret

# cPanel specific
.cpanel/
cpanel_logs/

# Passenger
tmp/restart.txt
