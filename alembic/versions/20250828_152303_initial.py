from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "20250828_152303_initial"
down_revision = None
branch_labels = None
depends_on = None

def upgrade() -> None:
    op.execute("CREATE EXTENSION IF NOT EXISTS pgcrypto")

    op.create_table('users',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('email', sa.String(320), nullable=False, unique=True),
        sa.Column('google_sub', sa.String(64), unique=True),
        sa.Column('name', sa.String(200)),
        sa.Column('avatar_url', sa.String(500)),
        sa.Column('is_allowed', sa.<PERSON>(), nullable=False, server_default=sa.text('false')),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now())
    )

    op.create_table('share_links',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('user_id', sa.UUID(as_uuid=True), sa.ForeignKey('users.id', ondelete="CASCADE"), nullable=False),
        sa.Column('token', sa.String(140), nullable=False, unique=True),
        sa.Column('title', sa.String(200)),
        sa.Column('note', sa.Text()),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default=sa.text('true')),
        sa.Column('expires_at', sa.TIMESTAMP(timezone=True)),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now())
    )

    op.create_table('hotels',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('name', sa.String(300), nullable=False),
        sa.Column('source_hotel_id', sa.String(200)),
        sa.Column('address', sa.String(500)),
        sa.Column('city', sa.String(120)),
        sa.Column('country', sa.String(100))
    )
    op.create_index('ix_hotels_name', 'hotels', ['name'], unique=False)
    op.create_index('ix_hotels_source_hotel_id', 'hotels', ['source_hotel_id'], unique=False)

    op.create_table('trips',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('hotel_id', sa.UUID(as_uuid=True), sa.ForeignKey('hotels.id', ondelete="CASCADE"), nullable=False),
        sa.Column('nights', sa.Integer()),
        sa.Column('tour_operator', sa.String(50)),
        sa.Column('outbound_date', sa.String(20)),
        sa.Column('outbound_dep_time', sa.String(20)),
        sa.Column('outbound_flight', sa.String(20)),
        sa.Column('outbound_arr_time', sa.String(20)),
        sa.Column('return_date', sa.String(20)),
        sa.Column('return_dep_time', sa.String(20)),
        sa.Column('return_flight', sa.String(20)),
        sa.Column('return_arr_time', sa.String(20)),
        sa.Column('price_text', sa.String(100)),
        sa.Column('comment', sa.Text()),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now())
    )

    op.create_table('share_link_trips',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('share_link_id', sa.UUID(as_uuid=True), sa.ForeignKey('share_links.id', ondelete="CASCADE"), nullable=False),
        sa.Column('trip_id', sa.UUID(as_uuid=True), sa.ForeignKey('trips.id', ondelete="CASCADE"), nullable=False),
        sa.Column('position', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now()),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now())
    )

    op.create_table('hotel_images',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('hotel_id', sa.UUID(as_uuid=True), sa.ForeignKey('hotels.id', ondelete="CASCADE"), nullable=False),
        sa.Column('url', sa.String(1000), nullable=False),
        sa.Column('width', sa.Integer()),
        sa.Column('height', sa.Integer()),
        sa.Column('attribution', sa.String(500)),
        sa.Column('provider', sa.String(50), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now())
    )

    op.create_table('hotel_reviews',
        sa.Column('id', sa.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('hotel_id', sa.UUID(as_uuid=True), sa.ForeignKey('hotels.id', ondelete="CASCADE"), nullable=False),
        sa.Column('review_id', sa.String(200)),
        sa.Column('reviewer_name', sa.String(200)),
        sa.Column('rating', sa.Float()),
        sa.Column('text', sa.Text(), nullable=False),
        sa.Column('date', sa.Date()),
        sa.Column('url', sa.String(1000)),
        sa.Column('provider', sa.String(50), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.func.now())
    )

def downgrade() -> None:
    op.drop_table('hotel_reviews')
    op.drop_table('hotel_images')
    op.drop_table('share_link_trips')
    op.drop_table('trips')
    op.drop_table('hotels')
    op.drop_table('share_links')
    op.drop_table('users')
