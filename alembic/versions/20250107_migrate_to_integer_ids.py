"""migrate to integer ids

Revision ID: 20250107_integer_ids
Revises: 20250107_convert_price
Create Date: 2025-01-07 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON


# revision identifiers, used by Alembic.
revision = '20250107_integer_ids'
down_revision = '20250107_convert_price'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop all existing tables (data will be lost as requested)
    op.execute("DROP TABLE IF EXISTS hotel_reviews CASCADE")
    op.execute("DROP TABLE IF EXISTS hotel_amenities CASCADE")
    op.execute("DROP TABLE IF EXISTS hotel_images CASCADE")
    op.execute("DROP TABLE IF EXISTS share_link_trips CASCADE")
    op.execute("DROP TABLE IF EXISTS trips CASCADE")
    op.execute("DROP TABLE IF EXISTS hotels CASCADE")
    op.execute("DROP TABLE IF EXISTS share_links CASCADE")
    op.execute("DROP TABLE IF EXISTS users CASCADE")
    
    # Recreate all tables with integer IDs
    
    # Users table
    op.create_table('users',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('email', sa.String(length=320), nullable=False),
        sa.Column('google_sub', sa.String(length=64), nullable=True),
        sa.Column('name', sa.String(length=200), nullable=True),
        sa.Column('avatar_url', sa.String(length=500), nullable=True),
        sa.Column('is_allowed', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('google_sub')
    )
    
    # Share links table
    op.create_table('share_links',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('token', sa.String(length=140), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=True),
        sa.Column('note', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('token')
    )
    
    # Hotels table
    op.create_table('hotels',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(length=300), nullable=False),
        sa.Column('source_hotel_id', sa.String(length=200), nullable=True),
        sa.Column('address', sa.String(length=500), nullable=True),
        sa.Column('city', sa.String(length=120), nullable=True),
        sa.Column('country', sa.String(length=100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('ranking_out_of', sa.Integer(), nullable=True),
        sa.Column('ranking', sa.Integer(), nullable=True),
        sa.Column('rating', sa.Float(), nullable=True),
        sa.Column('num_reviews', sa.Integer(), nullable=True),
        sa.Column('subratings', JSON(), nullable=True),
        sa.Column('amenities', JSON(), nullable=True),
        sa.Column('web_url', sa.String(length=500), nullable=True),
        sa.Column('see_all_photos', sa.String(length=500), nullable=True),
        sa.Column('video_link', sa.String(length=500), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for hotels
    op.create_index('ix_hotels_name', 'hotels', ['name'])
    op.create_index('ix_hotels_city', 'hotels', ['city'])
    op.create_index('ix_hotels_country', 'hotels', ['country'])
    op.create_index('ix_hotels_source_hotel_id', 'hotels', ['source_hotel_id'])
    
    # Trips table
    op.create_table('trips',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('hotel_id', sa.Integer(), nullable=False),
        sa.Column('nights', sa.Integer(), nullable=True),
        sa.Column('tour_operator', sa.String(length=50), nullable=True),
        sa.Column('outbound_date', sa.String(length=20), nullable=True),
        sa.Column('outbound_dep_time', sa.String(length=20), nullable=True),
        sa.Column('outbound_flight', sa.String(length=20), nullable=True),
        sa.Column('outbound_arr_time', sa.String(length=20), nullable=True),
        sa.Column('return_date', sa.String(length=20), nullable=True),
        sa.Column('return_dep_time', sa.String(length=20), nullable=True),
        sa.Column('return_flight', sa.String(length=20), nullable=True),
        sa.Column('return_arr_time', sa.String(length=20), nullable=True),
        sa.Column('price', sa.Float(), nullable=True),
        sa.Column('room_desc', sa.Text(), nullable=True),
        sa.Column('comment', sa.Text(), nullable=True),
        sa.Column('people_per_trip', sa.String(length=100), nullable=True),
        sa.Column('full_price', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['hotel_id'], ['hotels.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Share link trips table
    op.create_table('share_link_trips',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('share_link_id', sa.Integer(), nullable=False),
        sa.Column('trip_id', sa.Integer(), nullable=False),
        sa.Column('position', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['share_link_id'], ['share_links.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['trip_id'], ['trips.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Hotel images table
    op.create_table('hotel_images',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('hotel_id', sa.Integer(), nullable=False),
        sa.Column('url', sa.String(length=1000), nullable=False),
        sa.Column('width', sa.Integer(), nullable=True),
        sa.Column('height', sa.Integer(), nullable=True),
        sa.Column('attribution', sa.String(length=500), nullable=True),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['hotel_id'], ['hotels.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Hotel amenities table
    op.create_table('hotel_amenities',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('hotel_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['hotel_id'], ['hotels.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Hotel reviews table
    op.create_table('hotel_reviews',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('hotel_id', sa.Integer(), nullable=False),
        sa.Column('review_id', sa.String(length=200), nullable=True),
        sa.Column('reviewer_name', sa.String(length=200), nullable=True),
        sa.Column('rating', sa.Float(), nullable=True),
        sa.Column('text', sa.Text(), nullable=False),
        sa.Column('date', sa.DateTime(), nullable=True),
        sa.Column('url', sa.String(length=1000), nullable=True),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.func.now(), nullable=False),
        sa.ForeignKeyConstraint(['hotel_id'], ['hotels.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade() -> None:
    # This migration cannot be easily reversed since we're changing ID types
    # Would need to recreate with UUIDs and migrate data back
    raise NotImplementedError("This migration cannot be automatically reversed")
