"""add unique constraint source_hotel_id

Revision ID: 20250903_add_unique_constraint
Revises: 20250831_add_room_desc
Create Date: 2025-09-03 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250903_add_unique_constraint'
down_revision = '20250831_add_room_desc'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # First, remove duplicate source_hotel_id entries (keep the first one by id)
    op.execute("""
        DELETE FROM hotels
        WHERE id NOT IN (
            SELECT DISTINCT ON (source_hotel_id) id
            FROM hotels
            WHERE source_hotel_id IS NOT NULL
            ORDER BY source_hotel_id, id
        ) AND source_hotel_id IS NOT NULL
    """)

    # Add unique constraint to source_hotel_id
    op.create_unique_constraint('uq_hotels_source_hotel_id', 'hotels', ['source_hotel_id'])


def downgrade() -> None:
    # Remove unique constraint from source_hotel_id
    op.drop_constraint('uq_hotels_source_hotel_id', 'hotels', type_='unique')
