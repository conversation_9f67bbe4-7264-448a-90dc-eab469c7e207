"""add hotel indexes

Revision ID: 20250903_add_indexes
Revises: 20250903_drop_amenities
Create Date: 2025-09-03 18:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250903_add_indexes'
down_revision = '20250903_add_hotel_details'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add indexes for better query performance (if they don't exist)
    try:
        op.create_index('ix_hotels_city', 'hotels', ['city'])
    except Exception:
        pass  # Index already exists

    try:
        op.create_index('ix_hotels_country', 'hotels', ['country'])
    except Exception:
        pass  # Index already exists

    try:
        op.create_index('ix_hotels_name', 'hotels', ['name'])
    except Exception:
        pass  # Index already exists


def downgrade() -> None:
    # Remove indexes
    op.drop_index('ix_hotels_name', 'hotels')
    op.drop_index('ix_hotels_country', 'hotels')
    op.drop_index('ix_hotels_city', 'hotels')
