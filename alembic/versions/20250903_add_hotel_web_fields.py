"""add hotel web fields

Revision ID: 20250903_add_web_fields
Revises: 20250903_add_indexes
Create Date: 2025-09-03 23:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250903_add_web_fields'
down_revision = '20250903_add_indexes'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add web_url and see_all_photos fields to hotels table
    op.add_column('hotels', sa.Column('web_url', sa.String(length=500), nullable=True))
    op.add_column('hotels', sa.Column('see_all_photos', sa.String(length=500), nullable=True))


def downgrade() -> None:
    # Remove web_url and see_all_photos fields from hotels table
    op.drop_column('hotels', 'see_all_photos')
    op.drop_column('hotels', 'web_url')
