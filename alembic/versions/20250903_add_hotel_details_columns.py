"""add hotel details columns

Revision ID: 20250903_add_hotel_details
Revises: 20250903_add_unique_constraint
Create Date: 2025-09-03 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '20250903_add_hotel_details'
down_revision = '20250903_add_unique_constraint'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add new columns to hotels table
    op.add_column('hotels', sa.Column('description', sa.Text(), nullable=True))
    op.add_column('hotels', sa.Column('ranking_out_of', sa.Integer(), nullable=True))
    op.add_column('hotels', sa.Column('ranking', sa.Integer(), nullable=True))
    op.add_column('hotels', sa.Column('rating', sa.Float(), nullable=True))
    op.add_column('hotels', sa.Column('num_reviews', sa.Integer(), nullable=True))
    op.add_column('hotels', sa.Column('subratings', postgresql.JSON(astext_type=sa.Text()), nullable=True))
    op.add_column('hotels', sa.Column('amenities', postgresql.JSON(astext_type=sa.Text()), nullable=True))


def downgrade() -> None:
    # Remove new columns from hotels table
    op.drop_column('hotels', 'amenities')
    op.drop_column('hotels', 'subratings')
    op.drop_column('hotels', 'num_reviews')
    op.drop_column('hotels', 'rating')
    op.drop_column('hotels', 'ranking')
    op.drop_column('hotels', 'ranking_out_of')
    op.drop_column('hotels', 'description')
