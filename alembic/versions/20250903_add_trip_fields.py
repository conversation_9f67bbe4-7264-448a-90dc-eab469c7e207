"""add trip fields

Revision ID: 20250903_add_trip_fields
Revises: 20250903_add_web_fields
Create Date: 2025-09-03 23:45:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250903_add_trip_fields'
down_revision = '20250903_add_web_fields'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add video_link field to hotels table
    op.add_column('hotels', sa.Column('video_link', sa.String(length=500), nullable=True))
    
    # Add people_per_trip and full_price fields to trips table
    op.add_column('trips', sa.Column('people_per_trip', sa.String(length=100), nullable=True))
    op.add_column('trips', sa.Column('full_price', sa.Float(), nullable=True))


def downgrade() -> None:
    # Remove fields from trips table
    op.drop_column('trips', 'full_price')
    op.drop_column('trips', 'people_per_trip')
    
    # Remove video_link field from hotels table
    op.drop_column('hotels', 'video_link')
