"""add room_desc to trips

Revision ID: 20250831_add_room_desc
Revises: 20250828_152303_initial
Create Date: 2025-08-31 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250831_add_room_desc'
down_revision = '20250828_152303_initial'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add room_desc column to trips table
    op.add_column('trips', sa.Column('room_desc', sa.Text(), nullable=True))


def downgrade() -> None:
    # Remove room_desc column from trips table
    op.drop_column('trips', 'room_desc')
