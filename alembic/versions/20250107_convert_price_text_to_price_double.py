"""convert price_text to price double

Revision ID: 20250107_convert_price
Revises: 20250903_add_trip_fields
Create Date: 2025-01-07 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20250107_convert_price'
down_revision = '20250903_add_trip_fields'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add new price column as double
    op.add_column('trips', sa.Column('price', sa.Float(), nullable=True))
    
    # Convert existing price_text data to numeric price
    # This will extract numeric values from strings like "$1,500" -> 1500.0
    op.execute("""
        UPDATE trips 
        SET price = CAST(
            REGEXP_REPLACE(
                REGEXP_REPLACE(price_text, '[^0-9.,]', '', 'g'),
                ',', '', 'g'
            ) AS FLOAT
        )
        WHERE price_text IS NOT NULL 
        AND REGEXP_REPLACE(REGEXP_REPLACE(price_text, '[^0-9.,]', '', 'g'), ',', '', 'g') ~ '^[0-9]+\.?[0-9]*$'
    """)
    
    # Drop the old price_text column
    op.drop_column('trips', 'price_text')


def downgrade() -> None:
    # Add back price_text column
    op.add_column('trips', sa.Column('price_text', sa.String(length=100), nullable=True))
    
    # Convert price back to price_text format
    op.execute("""
        UPDATE trips 
        SET price_text = '$' || CAST(price AS VARCHAR)
        WHERE price IS NOT NULL
    """)
    
    # Drop the price column
    op.drop_column('trips', 'price')
