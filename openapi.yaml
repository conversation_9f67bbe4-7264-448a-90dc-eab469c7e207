openapi: 3.0.3
info:
  title: Sirev Proposal Generator API
  version: "0.1.0"
  description: |
    Backend for generating shareable hotel proposals collected via the Chrome extension.

    **Key points**
    - Auth via Google ID token verification; app issues JWT (access + refresh).
    - `POST /hotel-trips:bulk` saves hotels, runs **inline enrichment**, creates a **share link**, and returns the share URL.
    - `GET /s/{token}` serves a **public HTML** page; no auth required.
    - **Images**: only **URLs** are stored (no file uploads).
    - **Timestamps**: `departure_timestamp` and `return_timestamp` are stored and returned **as-is** (no conversion), as 64-bit integers.

servers:
  - url: https://api.yourdomain.com
    description: Production
  - url: https://staging.api.yourdomain.com
    description: Staging
tags:
  - name: Auth
    description: Authentication & session endpoints
  - name: Hotels & Share
    description: Submit hotels, create share link, and related operations
  - name: Public
    description: Public, tokenized access

paths:
  /auth/google/verify:
    post:
      tags: [Auth]
      summary: Verify Google ID token and issue application tokens
      description: |
        Verifies a Google **ID token** (OpenID Connect). If the user exists with `is_allowed=true`, returns app **JWT** tokens.
        If user is new, auto-provisions with `is_allowed=false` and returns **403** until the flag is enabled server-side.
      operationId: app.controllers.auth.google_verify_sync
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GoogleVerifyRequest'
            examples:
              default:
                summary: Example request
                value:
                  id_token: eyJhbGciOiJSUzI1NiIsImtpZCI6IjEyMzQifQ.****************************************************************************************************************.sig
      responses:
        '200':
          description: Tokens issued
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthSuccess'
              examples:
                success:
                  value:
                    access_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                    refresh_token: dXNlcl9yZWZyZXNoX3Rva2VuX2V4YW1wbGU=
                    token_type: Bearer
                    expires_in: 3600
                    user:
                      id: "5f0c2b3f-6e7d-4a5a-9d0f-1d2c3b4a5e6f"
                      email: <EMAIL>
                      google_sub: "10234567890"
                      name: "Alex Doe"
                      avatar_url: "https://lh3.googleusercontent.com/a/AA..."
                      is_allowed: true
        '403':
          description: User exists but not allowed yet (is_allowed=false)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                not_allowed:
                  value:
                    code: FORBIDDEN
                    message: User is not allowed. Contact an administrator.
        '400':
          description: Bad request (e.g., malformed token)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/refresh:
    post:
      tags: [Auth]
      summary: Refresh access token
      operationId: app.controllers.auth.refresh_token_sync
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshRequest'
            examples:
              default:
                value:
                  refresh_token: dXNlcl9yZWZyZXNoX3Rva2VuX2V4YW1wbGU=
      responses:
        '200':
          description: New access token issued
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshResponse'
              examples:
                success:
                  value:
                    access_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                    token_type: Bearer
                    expires_in: 3600
        '401':
          description: Invalid or expired refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /me:
    get:
      tags: [Auth]
      summary: Get current user
      operationId: app.controllers.auth.me_sync
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Current user
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
              examples:
                example:
                  value:
                    id: "5f0c2b3f-6e7d-4a5a-9d0f-1d2c3b4a5e6f"
                    email: <EMAIL>
                    google_sub: "10234567890"
                    name: "Alex Doe"
                    avatar_url: "https://lh3.googleusercontent.com/a/AA..."
                    is_allowed: true
        '401':
          description: Missing/invalid token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /hotel-trips:bulk:
    post:
      tags: [Hotels & Share]
      summary: Submit selected hotels and create a shareable link
      description: |
        Saves the provided hotels, performs **inline enrichment** (images, amenities, reviews), creates a **share link**, and returns the public URL.

        - `hotels` must follow the exact payload contract below.
        - **Per-row comments** should be supplied in `items[]` (aligned by index with `hotels[]`).
        - Max hotels per request is implementation-defined (e.g., 50).
      operationId: app.controllers.hotels.submit_trips_bulk_sync
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkSubmitRequest'
            examples:
              minimal:
                value:
                  hotels:
                    - name: "Hotel Example"
                      source_hotel_id: "123"
                      address: "1 Beach Ave, Miami, FL"
                      city: "Miami"
                      country: "US"
                      price_value: 219.0
                      departure_timestamp: 1756350422
                      return_timestamp: 1756350422
                      nights: 8
                      room_type: "All Inclusive (Double)"
                  items:
                    - comment: "Near venue; good value"
                  title: "Conference trip proposals"
                  note: "Prices from sirev.com; taxes included where shown."
              multiple:
                value:
                  title: "Family Trip Options"
                  note: "Generated via extension"
                  hotels:
                    - name: "Iberostar Waves Paraiso Beach"
                      source_hotel_id: "1003-7n"
                      address: "Riviera Maya, Mexico"
                      city: "Riviera Maya"
                      country: "MX"
                      price_value: 3154.0
                      departure_timestamp: 1756339200
                      return_timestamp: 1756857600
                      nights: 7
                      room_type: "All Inclusive (Double)"
                    - name: "Iberostar Waves Paraiso Beach"
                      source_hotel_id: "1003-8n"
                      address: "Riviera Maya, Mexico"
                      city: "Riviera Maya"
                      country: "MX"
                      price_value: 3065.0
                      departure_timestamp: 1756339200
                      return_timestamp: 1756944000
                      nights: 8
                      room_type: "All Inclusive (Double)"
                  items:
                    - comment: "Better flight times"
                    - comment: "Slightly cheaper"
      responses:
        '200':
          description: Share link created; enrichment attempted inline
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkSubmitResponse'
              examples:
                success:
                  value:
                    share_url: "https://api.yourdomain.com/s/2hcq7l7HnC5fwe8mKXr9xA"
                    link_id: "7428a5db-6a1e-4f49-9c94-8a9b87bafc0a"
                    link_token: "2hcq7l7HnC5fwe8mKXr9xA"
                    hotels_saved: 2
                    enrichment:
                      ok: 2
                      failed: 0
        '400':
          description: Invalid request (e.g., array lengths mismatch)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                mismatch:
                  value:
                    code: BAD_REQUEST
                    message: "items length (1) does not match hotels length (2)."
        '401':
          description: Missing/invalid auth token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation error (payload does not match contract)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '500':
          description: Internal error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /s/{token}:
    get:
      tags: [Public]
      summary: Public share page (HTML)
      description: Returns a server-rendered HTML page for the given share token.
      operationId: app.controllers.public.share_page_sync
      parameters:
        - name: token
          in: path
          required: true
          description: Share token returned by `/hotel-trips:bulk`
          schema:
            type: string
            minLength: 8
            maxLength: 128
      responses:
        '200':
          description: HTML page
          content:
            text/html:
              schema:
                type: string
                description: HTML content
        '404':
          description: Token not found or inactive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    GoogleVerifyRequest:
      type: object
      required: [id_token]
      properties:
        id_token:
          type: string
          description: Google OpenID Connect ID token (JWT)
    AuthSuccess:
      type: object
      required: [access_token, refresh_token, token_type, expires_in, user]
      properties:
        access_token:
          type: string
          description: Application JWT access token
        refresh_token:
          type: string
        token_type:
          type: string
          example: Bearer
        expires_in:
          type: integer
          format: int32
          description: Expiration in seconds
          example: 3600
        user:
          $ref: '#/components/schemas/User'

    RefreshRequest:
      type: object
      required: [refresh_token]
      properties:
        refresh_token:
          type: string
    RefreshResponse:
      type: object
      required: [access_token, token_type, expires_in]
      properties:
        access_token:
          type: string
        token_type:
          type: string
          example: Bearer
        expires_in:
          type: integer
          format: int32
          example: 3600

    User:
      type: object
      required: [id, email, is_allowed]
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        google_sub:
          type: string
          description: Google account subject (sub)
        name:
          type: string
          nullable: true
        avatar_url:
          type: string
          format: uri
          nullable: true
        is_allowed:
          type: boolean
          description: Gate flag to allow access

    HotelPayload:
      type: object
      description: Exact hotel payload expected from the extension
      required:
        - name
        - source_hotel_id
        - address
        - city
        - country
        - price_value
        - departure_timestamp
        - return_timestamp
        - nights
        - room_type
      properties:
        name:
          type: string
          example: "Iberostar Waves Paraiso Beach"
        source_hotel_id:
          type: string
          example: "1003-8n"
        address:
          type: string
          example: "Riviera Maya, Mexico"
        city:
          type: string
          example: "Riviera Maya"
        country:
          type: string
          example: "MX"
        price_value:
          type: number
          format: float
          example: 3065.0
        departure_timestamp:
          type: integer
          format: int64
          description: Stored and returned **as-is** (no conversion)
          example: 1756339200
        return_timestamp:
          type: integer
          format: int64
          description: Stored and returned **as-is** (no conversion)
          example: 1756944000
        nights:
          type: integer
          format: int32
          example: 8
        room_type:
          type: string
          example: "All Inclusive (Double)"

    BulkItem:
      type: object
      description: Per-row metadata aligned by index with `hotels[]`
      properties:
        comment:
          type: string
          maxLength: 2000
          example: "Close to the conference venue"

    BulkSubmitRequest:
      type: object
      required: [hotels]
      properties:
        title:
          type: string
          maxLength: 200
          nullable: true
        note:
          type: string
          maxLength: 2000
          nullable: true
        hotels:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/HotelPayload'
          description: |
            Array length limit is implementation-defined (e.g., 50).
        items:
          type: array
          nullable: true
          description: |
            Optional array aligned by index with `hotels[]` for per-row metadata (e.g., comments).
            If provided, `items.length` **must match** `hotels.length`.
          items:
            $ref: '#/components/schemas/BulkItem'

    BulkSubmitResponse:
      type: object
      required: [share_url, link_id, link_token, hotels_saved, enrichment]
      properties:
        share_url:
          type: string
          format: uri
          example: "https://api.yourdomain.com/s/2hcq7l7HnC5fwe8mKXr9xA"
        link_id:
          type: string
          format: uuid
        link_token:
          type: string
          minLength: 8
          maxLength: 128
        hotels_saved:
          type: integer
          format: int32
        enrichment:
          $ref: '#/components/schemas/EnrichmentSummary'

    EnrichmentSummary:
      type: object
      required: [ok, failed]
      properties:
        ok:
          type: integer
          format: int32
          example: 2
        failed:
          type: integer
          format: int32
          example: 0

    Hotel:
      type: object
      description: Canonical hotel row persisted in the database
      allOf:
        - $ref: '#/components/schemas/HotelPayload'
        - type: object
          properties:
            id:
              type: string
              format: uuid
            created_at:
              type: string
              format: date-time
            updated_at:
              type: string
              format: date-time

    ShareLink:
      type: object
      properties:
        id:
          type: string
          format: uuid
        user_id:
          type: string
          format: uuid
        token:
          type: string
        title:
          type: string
          nullable: true
        note:
          type: string
          nullable: true
        is_active:
          type: boolean
        expires_at:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time

    HotelImage:
      type: object
      properties:
        url:
          type: string
          format: uri
        width:
          type: integer
          format: int32
          nullable: true
        height:
          type: integer
          format: int32
          nullable: true
        attribution:
          type: string
          nullable: true
        provider:
          type: string

    HotelAmenity:
      type: object
      properties:
        name:
          type: string
        provider:
          type: string

    HotelReview:
      type: object
      properties:
        review_id:
          type: string
          nullable: true
        reviewer_name:
          type: string
          nullable: true
        rating:
          type: number
          format: float
          nullable: true
        text:
          type: string
        date:
          type: string
          format: date
          nullable: true
        url:
          type: string
          format: uri
          nullable: true
        provider:
          type: string

    Error:
      type: object
      required: [code, message]
      properties:
        code:
          type: string
          example: BAD_REQUEST
        message:
          type: string
          example: Something went wrong.
        details:
          description: Optional machine-readable details
          nullable: true
    ValidationError:
      type: object
      properties:
        code:
          type: string
          example: UNPROCESSABLE_ENTITY
        message:
          type: string
          example: Payload validation failed.
        errors:
          type: array
          items:
            type: object
            properties:
              loc:
                type: array
                items:
                  type: string
                example: ["body", "hotels", "0", "name"]
              type:
                type: string
                example: value_error.missing
