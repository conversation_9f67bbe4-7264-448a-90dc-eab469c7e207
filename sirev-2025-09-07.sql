--
-- PostgreSQL database dump
--
-- Dumped from database version 16.10 (Debian 16.10-1.pgdg13+1)
-- Dumped by pg_dump version 16.10 (Debian 16.10-1.pgdg13+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--

--

--

--

SET default_tablespace = '';

--

--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);




--

--

CREATE TABLE public.hotel_amenities (
    id integer NOT NULL,
    hotel_id integer NOT NULL,
    name character varying(200) NOT NULL,
    provider character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);




--

--

CREATE SEQUENCE public.hotel_amenities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--

--

ALTER SEQUENCE public.hotel_amenities_id_seq OWNED BY public.hotel_amenities.id;


--

--

CREATE TABLE public.hotel_images (
    id integer NOT NULL,
    hotel_id integer NOT NULL,
    url character varying(1000) NOT NULL,
    width integer,
    height integer,
    attribution character varying(500),
    provider character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);




--

--

CREATE SEQUENCE public.hotel_images_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--

--

ALTER SEQUENCE public.hotel_images_id_seq OWNED BY public.hotel_images.id;


--

--

CREATE TABLE public.hotel_reviews (
    id integer NOT NULL,
    hotel_id integer NOT NULL,
    review_id character varying(200),
    reviewer_name character varying(200),
    rating double precision,
    text text NOT NULL,
    date timestamp without time zone,
    url character varying(1000),
    provider character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);




--

--

CREATE SEQUENCE public.hotel_reviews_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--

--

ALTER SEQUENCE public.hotel_reviews_id_seq OWNED BY public.hotel_reviews.id;


--

--

CREATE TABLE public.hotels (
    id integer NOT NULL,
    name character varying(300) NOT NULL,
    source_hotel_id character varying(200),
    address character varying(500),
    city character varying(120),
    country character varying(100),
    description text,
    ranking_out_of integer,
    ranking integer,
    rating double precision,
    num_reviews integer,
    subratings json,
    amenities json,
    web_url character varying(500),
    see_all_photos character varying(500),
    video_link character varying(500)
);




--

--

CREATE SEQUENCE public.hotels_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--

--

ALTER SEQUENCE public.hotels_id_seq OWNED BY public.hotels.id;


--

--

CREATE TABLE public.share_link_trips (
    id integer NOT NULL,
    share_link_id integer NOT NULL,
    trip_id integer NOT NULL,
    "position" integer NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);




--

--

CREATE SEQUENCE public.share_link_trips_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--

--

ALTER SEQUENCE public.share_link_trips_id_seq OWNED BY public.share_link_trips.id;


--

--

CREATE TABLE public.share_links (
    id integer NOT NULL,
    user_id integer NOT NULL,
    token character varying(140) NOT NULL,
    title character varying(200),
    note text,
    is_active boolean NOT NULL,
    expires_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);




--

--

CREATE SEQUENCE public.share_links_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--

--

ALTER SEQUENCE public.share_links_id_seq OWNED BY public.share_links.id;


--

--

CREATE TABLE public.trips (
    id integer NOT NULL,
    hotel_id integer NOT NULL,
    nights integer,
    tour_operator character varying(50),
    outbound_date character varying(20),
    outbound_dep_time character varying(20),
    outbound_flight character varying(20),
    outbound_arr_time character varying(20),
    return_date character varying(20),
    return_dep_time character varying(20),
    return_flight character varying(20),
    return_arr_time character varying(20),
    price double precision,
    room_desc text,
    comment text,
    people_per_trip character varying(100),
    full_price double precision,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);




--

--

CREATE SEQUENCE public.trips_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--

--

ALTER SEQUENCE public.trips_id_seq OWNED BY public.trips.id;


--

--

CREATE TABLE public.users (
    id integer NOT NULL,
    email character varying(320) NOT NULL,
    google_sub character varying(64),
    name character varying(200),
    avatar_url character varying(500),
    is_allowed boolean NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL
);




--

--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--

--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--

--

ALTER TABLE ONLY public.hotel_amenities ALTER COLUMN id SET DEFAULT nextval('public.hotel_amenities_id_seq'::regclass);


--

--

ALTER TABLE ONLY public.hotel_images ALTER COLUMN id SET DEFAULT nextval('public.hotel_images_id_seq'::regclass);


--

--

ALTER TABLE ONLY public.hotel_reviews ALTER COLUMN id SET DEFAULT nextval('public.hotel_reviews_id_seq'::regclass);


--

--

ALTER TABLE ONLY public.hotels ALTER COLUMN id SET DEFAULT nextval('public.hotels_id_seq'::regclass);


--

--

ALTER TABLE ONLY public.share_link_trips ALTER COLUMN id SET DEFAULT nextval('public.share_link_trips_id_seq'::regclass);


--

--

ALTER TABLE ONLY public.share_links ALTER COLUMN id SET DEFAULT nextval('public.share_links_id_seq'::regclass);


--

--

ALTER TABLE ONLY public.trips ALTER COLUMN id SET DEFAULT nextval('public.trips_id_seq'::regclass);


--

--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--

--

COPY public.alembic_version (version_num) FROM stdin;
20250107_integer_ids
\.


--

--

COPY public.hotel_amenities (id, hotel_id, name, provider, created_at) FROM stdin;
\.


--

--

COPY public.hotel_images (id, hotel_id, url, width, height, attribution, provider, created_at) FROM stdin;
\.


--

--

COPY public.hotel_reviews (id, hotel_id, review_id, reviewer_name, rating, text, date, url, provider, created_at) FROM stdin;
\.


--

--

COPY public.hotels (id, name, source_hotel_id, address, city, country, description, ranking_out_of, ranking, rating, num_reviews, subratings, amenities, web_url, see_all_photos, video_link) FROM stdin;
\.


--

--

COPY public.share_link_trips (id, share_link_id, trip_id, "position", created_at, updated_at) FROM stdin;
\.


--

--

COPY public.share_links (id, user_id, token, title, note, is_active, expires_at, created_at) FROM stdin;
1	1	XCTkjiB2EBw8tuKOeHUvIxJP	\N	\N	t	\N	2025-09-07 14:48:15.267415
2	1	4b5kOAZYWQS5ZJ0nqO4m8g3R	\N	\N	t	\N	2025-09-07 14:49:25.725078
3	2	3rGd4UBHWexByQIbMFaf33fh	\N	\N	t	\N	2025-09-07 18:24:37.5077
\.


--

--

COPY public.trips (id, hotel_id, nights, tour_operator, outbound_date, outbound_dep_time, outbound_flight, outbound_arr_time, return_date, return_dep_time, return_flight, return_arr_time, price, room_desc, comment, people_per_trip, full_price, created_at, updated_at) FROM stdin;
\.


--

--

COPY public.users (id, email, google_sub, name, avatar_url, is_allowed, created_at, updated_at) FROM stdin;
1	<EMAIL>	<EMAIL>	test	\N	t	2025-09-07 14:46:11.341801	2025-09-07 14:46:11.341801
2	<EMAIL>	102116638692207622439	Ruslan Salii	https://lh3.googleusercontent.com/a/ACg8ocKCS0lqCi4aZZc3XMLvz6jDJKMRP8tX8oKIhwhkJKlghmaGNj5x=s96-c	t	2025-09-07 18:13:11.187876	2025-09-07 18:13:11.187876
\.


--

--

SELECT pg_catalog.setval('public.hotel_amenities_id_seq', 1, false);


--

--

SELECT pg_catalog.setval('public.hotel_images_id_seq', 20, true);


--

--

SELECT pg_catalog.setval('public.hotel_reviews_id_seq', 7, true);


--

--

SELECT pg_catalog.setval('public.hotels_id_seq', 3, true);


--

--

SELECT pg_catalog.setval('public.share_link_trips_id_seq', 4, true);


--

--

SELECT pg_catalog.setval('public.share_links_id_seq', 3, true);


--

--

SELECT pg_catalog.setval('public.trips_id_seq', 4, true);


--

--

SELECT pg_catalog.setval('public.users_id_seq', 2, true);


--

--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--

--

ALTER TABLE ONLY public.hotel_amenities
    ADD CONSTRAINT hotel_amenities_pkey PRIMARY KEY (id);


--

--

ALTER TABLE ONLY public.hotel_images
    ADD CONSTRAINT hotel_images_pkey PRIMARY KEY (id);


--

--

ALTER TABLE ONLY public.hotel_reviews
    ADD CONSTRAINT hotel_reviews_pkey PRIMARY KEY (id);


--

--

ALTER TABLE ONLY public.hotels
    ADD CONSTRAINT hotels_pkey PRIMARY KEY (id);


--

--

ALTER TABLE ONLY public.share_link_trips
    ADD CONSTRAINT share_link_trips_pkey PRIMARY KEY (id);


--

--

ALTER TABLE ONLY public.share_links
    ADD CONSTRAINT share_links_pkey PRIMARY KEY (id);


--

--

ALTER TABLE ONLY public.share_links
    ADD CONSTRAINT share_links_token_key UNIQUE (token);


--

--

ALTER TABLE ONLY public.trips
    ADD CONSTRAINT trips_pkey PRIMARY KEY (id);


--

--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--

--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_google_sub_key UNIQUE (google_sub);


--

--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--

--

CREATE INDEX ix_hotels_city ON public.hotels USING btree (city);


--

--

CREATE INDEX ix_hotels_country ON public.hotels USING btree (country);


--

--

CREATE INDEX ix_hotels_name ON public.hotels USING btree (name);


--

--

CREATE INDEX ix_hotels_source_hotel_id ON public.hotels USING btree (source_hotel_id);


--

--

ALTER TABLE ONLY public.hotel_amenities
    ADD CONSTRAINT hotel_amenities_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES public.hotels(id) ON DELETE CASCADE;


--

--

ALTER TABLE ONLY public.hotel_images
    ADD CONSTRAINT hotel_images_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES public.hotels(id) ON DELETE CASCADE;


--

--

ALTER TABLE ONLY public.hotel_reviews
    ADD CONSTRAINT hotel_reviews_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES public.hotels(id) ON DELETE CASCADE;


--

--

ALTER TABLE ONLY public.share_link_trips
    ADD CONSTRAINT share_link_trips_share_link_id_fkey FOREIGN KEY (share_link_id) REFERENCES public.share_links(id) ON DELETE CASCADE;


--

--

ALTER TABLE ONLY public.share_link_trips
    ADD CONSTRAINT share_link_trips_trip_id_fkey FOREIGN KEY (trip_id) REFERENCES public.trips(id) ON DELETE CASCADE;


--

--

ALTER TABLE ONLY public.share_links
    ADD CONSTRAINT share_links_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--

--

ALTER TABLE ONLY public.trips
    ADD CONSTRAINT trips_hotel_id_fkey FOREIGN KEY (hotel_id) REFERENCES public.hotels(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--


