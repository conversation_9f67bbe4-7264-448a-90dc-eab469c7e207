#!/usr/bin/env python3
"""
WSGI entry point for cPanel shared hosting with Passenger.
This file should be placed in the root directory of your cPanel application.
"""

import sys
import os
from pathlib import Path

# Add the application directory to Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

# Set environment variables for production
os.environ.setdefault('ENV', 'production')
os.environ.setdefault('PYTHONPATH', str(app_dir))

# Load environment variables from .env file if it exists
env_file = app_dir / '.env'
if env_file.exists():
    from dotenv import load_dotenv
    load_dotenv(env_file)

# Import the Flask application
try:
    # Set production environment before importing
    os.environ.setdefault('ENV', 'production')

    # Import the pre-created application
    from app.main import connexion_app

    # Get the underlying Flask app for WSGI
    application = connexion_app.app

    # Configure for production
    application.config['ENV'] = 'production'
    application.config['DEBUG'] = False

    # Test that the application works
    with application.test_client() as client:
        response = client.get('/healthz')
        if response.status_code != 200:
            raise Exception(f"Health check failed: {response.status_code}")

except Exception as e:
    import traceback

    # Create a simple error application for debugging
    def application(environ, start_response):
        status = '500 Internal Server Error'
        headers = [('Content-type', 'text/html')]
        start_response(status, headers)

        error_msg = f"""
        <html>
        <head><title>Application Error</title></head>
        <body>
        <h1>Application Error</h1>
        <p><strong>Error:</strong> {str(e)}</p>
        <p><strong>Python path:</strong> {sys.path}</p>
        <p><strong>Working directory:</strong> {os.getcwd()}</p>
        <p><strong>App directory:</strong> {app_dir}</p>
        <p><strong>Environment variables:</strong></p>
        <ul>
        """

        for key, value in os.environ.items():
            if 'SECRET' not in key and 'PASSWORD' not in key:
                error_msg += f"<li>{key}: {value}</li>"

        error_msg += f"""
        </ul>
        <p><strong>Traceback:</strong></p>
        <pre>{traceback.format_exc()}</pre>
        </body>
        </html>
        """

        return [error_msg.encode('utf-8')]

# For debugging - you can remove this in production
if __name__ == '__main__':
    print("This is the WSGI entry point for Passenger.")
    print("It should not be run directly.")
    print(f"Application directory: {app_dir}")
    print(f"Python path: {sys.path}")
