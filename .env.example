# --- Server ---
APP_NAME=Sirev Proposal Generator API
ENV=dev
HOST=0.0.0.0
PORT=8000

# --- CORS ---
# Comma-separated list of allowed origins
CORS_ALLOW_ORIGINS=chrome-extension://<EXT_ID>,https://www.sirev.com,http://localhost:3000

# --- Database ---
DATABASE_URL=postgresql+asyncpg://postgres:postgres@db:5432/sirev

# --- Auth ---
GOOGLE_CLIENT_ID=your-google-oauth-client-id.apps.googleusercontent.com
JWT_SECRET=dev-secret-change-me
JWT_REFRESH_SECRET=dev-refresh-secret-change-me
JWT_ISSUER=sirev-proposal
JWT_AUDIENCE=sirev-proposal-users
ACCESS_TOKEN_EXPIRES_SECONDS=3600
REFRESH_TOKEN_EXPIRES_SECONDS=1209600

# Dev bypass (ONLY in dev): if set to true, /auth/google/verify accepts X-<PERSON>-<PERSON><PERSON>
DEV_AUTH_BYPASS=true

# --- Enrichment ---
ENRICH_PROVIDER=mock   # options: mock
ENRICH_CONCURRENCY=6
ENRICH_PER_CALL_TIMEOUT_SECONDS=5
ENRICH_GLOBAL_TIMEOUT_SECONDS=25
