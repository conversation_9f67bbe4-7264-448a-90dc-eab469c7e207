FROM python:3.12-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

WORKDIR /app

# System deps
RUN apt-get update && apt-get install -y --no-install-recommends     build-essential curl     && rm -rf /var/lib/apt/lists/*

COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# Expose port
EXPOSE 8000

# Default env
ENV ENV=dev
ENV HOST=0.0.0.0
ENV PORT=8000

CMD ["bash", "-lc", "alembic upgrade head && gunicorn --bind ${HOST}:${PORT} --workers 4 --worker-class gevent --worker-connections 1000 --timeout 120 app.main:app" ]
