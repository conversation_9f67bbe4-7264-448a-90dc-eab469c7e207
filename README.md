# Sirev Proposal Generator — Backend

FastAPI + PostgreSQL backend for collecting hotel selections from a Chrome extension, enriching them inline, and generating shareable proposal pages.

## Quickstart (Docker Compose)/  commads

```bash
# from project root
docker compose up --build -d
# apply migrations (Container runs alembic upgrade head automatically on start)
```

[//]: # ($ cd /Users/<USER>/PycharmProjects/Sirev/Backend && docker-compose exec api alembic revision -m "add_room_desc_to_trips")

Open: http://localhost:8000/docs

### Environment

Copy `.env.example` to `.env` (or edit `.env.example` directly; compose loads it).

Key variables:
- `CORS_ALLOW_ORIGINS` — include your extension origin `chrome-extension://<EXT_ID>` and `https://www.sirev.com`.
- `DEV_AUTH_BYPASS=true` — allows `/auth/google/verify` to accept `X-Dev-Email` in dev.

### Dev Auth Bypass

```bash
curl -X POST http://localhost:8000/auth/google/verify   -H "Content-Type: application/json"   -H "X-Dev-Email: <EMAIL>"   -d '{"id_token":"dev"}'
# -> returns access/refresh tokens IF the user has is_allowed=true in DB
```

> New users are created with `is_allowed=false`. Flip it for your dev account:

```sql
-- in psql against the 'sirev' database
UPDATE users SET is_allowed = TRUE WHERE email = '<EMAIL>';
```

### Bulk Submit

```bash
curl -X POST http://localhost:8000/hotel-trips:bulk   -H "Authorization: Bearer <ACCESS_TOKEN>"   -H "Content-Type: application/json"   -d '{
    "title": "Conference trip proposals",
    "note": "Generated locally",
    "hotels": [{
      "name":"Hotel Example",
      "source_hotel_id":"123",
      "address":"1 Beach Ave",
      "city":"Miami",
      "country":"US",
      "price_value":219.0,
      "departure_timestamp":**********,
      "return_timestamp":**********,
      "nights":8,
      "room_type":"All Inclusive (Double)"
    }],
    "items":[{"comment":"Near venue"}]
  }'
```

Open the returned `share_url` in your browser.

## Local (without Docker)

```bash
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
export DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/sirev
alembic upgrade head
uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

## Notes

- **Enrichment** uses a **mock provider** that returns placeholder images and basic amenities/reviews; replace `app/services/enrichment.py` with real provider integrations when available.
- Timestamps are stored and returned **as-is** (bigint).
- Images are **URL-only**; no file storage is used.

## License


# Requests to the application

## Health check
curl http://localhost:8000/healthz
{"ok":true,"env":"dev"}

## Authentication
curl -X POST http://localhost:8000/auth/google/verify \
  -H "x-dev-email: <EMAIL>" \
  -d '{"id_token": "dummy-token"}'
### Returns: access_token, refresh_token, user info

## Trip submission (extension format)
curl -X POST http://localhost:8000/hotel-trips:bulk \
  -H "Authorization: Bearer <token>" \
  -d '[{"name": "Hotel Example", "location": "Montreal, Canada", ...}]'
#### Returns: {"share_url": "http://localhost:8000/s/...", "trips_saved": 1}

### Share page access
curl http://localhost:8000/s/<token>
# Returns: HTML page with hotel details

## Backend todo:
* create and endpoint to load a proposal by token. 
* set links to expire after 90 days.
* start collecting overall rating from TripAdvisor
* do we need a standalone table for ameties or we can just store it as json ?
* 
* can I delete config.py ?


## Extension todo:
* remove all extra characters from date and flight
  * WS2710 
  * SEP 09
* grabb new field about the room type from UI and store it in BE

## issues to fix
* [Sirev Proposal Generator CS] enhanceAll failed: TypeError: Cannot read properties of undefined (reading 'local')

## Roma update
* photos from TripAdvisor only 5 and without tags


# Requests to TripAdvisor

1) Search a hotel by name + location → get location_id
curl -G 'https://api.content.tripadvisor.com/api/v1/location/search' \
  --data-urlencode "key=$TRIPADVISOR_API_KEY" \
  --data-urlencode "searchQuery=Melia Caribe Beach Resort, Punta Cana, Dominican Republic" \
  --data-urlencode "category=hotels" \
  --data-urlencode "language=$TA_LANG" \
  -H 'accept: application/json' | jq

curl -G 'https://api.content.tripadvisor.com/api/v1/location/search' \
  --data-urlencode "key=$TRIPADVISOR_API_KEY" \
  --data-urlencode "searchQuery=Hm Playa Del Carmen, Playa del Carmen, Mexico" \
  --data-urlencode "category=hotels" \
  --data-urlencode "language=$TA_LANG" \
  -H 'accept: application/json' | jq

curl --request GET \
     --url 'https://api.content.tripadvisor.com/api/v1/location/search?key=A21A0C3E90C34992A3C59B4D1382C2BE&searchQuery=Melia%20Caribe%20Beach%20Resort%2C%20Punta%20Cana%2C%20Dominican%20Republic&language=en' \
     --header 'accept: application/json' | jq

searchQuery=Melia Caribe Beach Resort, Punta Cana, Dominican Republic&language=en' \

Use searchQuery with the hotel name + city/country, and category=hotels.

From the JSON response, take the best match’s location_id. (That location_id is what the details/photos/reviews endpoints use.) 
Tripadvisor Content API

Tip: If you have jq, you can grab the first result’s ID like this:

LOC_ID=$(curl -Gs 'https://api.content.tripadvisor.com/api/v1/location/search' \
  --data-urlencode "key=$TRIPADVISOR_API_KEY" \
  --data-urlencode "searchQuery=Iberostar Waves Paraiso Beach Riviera Maya, Mexico" \
  --data-urlencode "category=hotels" \
  --data-urlencode "language=$TA_LANG" |
  jq -r '.data[0].location_12id')
echo "$LOC_ID"


(Structure may vary slightly; if .data isn’t present, inspect the payload and adjust the path.) 
Tripadvisor Content API

2) Get hotel details (name, address, rating, TA URLs)
LOC_ID=151390
curl -G "https://api.content.tripadvisor.com/api/v1/location/${LOC_ID}/details" \
  --data-urlencode "key=$TRIPADVISOR_API_KEY" \
  --data-urlencode "language=$TA_LANG" \
  -H 'accept: application/json'


Tripadvisor Content API

3) Get hotel photos (URLs in multiple sizes)
curl -G "https://api.content.tripadvisor.com/api/v1/location/${LOC_ID}/photos?&source=Expert" \
  --data-urlencode "key=$TRIPADVISOR_API_KEY" \
  --data-urlencode "language=$TA_LANG" \
  -H 'accept: application/json'


(Photos are ordered by recency; API typically returns up to 5.) 
Tripadvisor Content API

4) Get recent reviews
curl -G "https://api.content.tripadvisor.com/api/v1/location/${LOC_ID}/reviews" \
  --data-urlencode "key=$TRIPADVISOR_API_KEY" \
  --data-urlencode "language=$TA_LANG" \
  --data-urlencode "limit=5" \
  --data-urlencode "offset=0" \
  -H 'accept: application/json'



https://accounts.google.com/o/oauth2/v2/auth?client_id=************-7g4m3l80bf4eujjpdqveg5efge75pkhh.apps.googleusercontent.com&redirect_uri=https://glecjnlgconheeinilkfeojkfafdflpd.chromiumapp.org&response_type=id_token&scope=openid%20email%20profile&nonce=***********

response_type=id_token (returns an ID token directly to the extension)

scope=openid email profile

nonce=<random-string> (required for OIDC)

Optional UX: prompt=select_account (or consent)


Contract with Backend

[
  {
    "name": "Hotel Example",
    "location": "Montreal, Canada",
    "nights": 6,
    "tur_operator": "SWG",
    "outboundDate": "SEP 09",
    "outboundDepTime": "15:30",
    "outboundFlight": "WS2710",
    "outboundArrTime": "19:00",
    "returnDate": "SEP 14",
    "returnDepTime": "19:30",
    "returnFlight": "WS2711",
    "returnArrTime": "01:12",
    "price": "$1170",
    "comment": ""
  },
  {
    "name": "Hotel Example2",
    "location": "Torronto, Canada",
    "nights": 6,
    "tur_operator": "SWG",
    "outboundDate": "SEP 10",
    "outboundDepTime": "12:30",
    "outboundFlight": "WS2510",
    "outboundArrTime": "19:10",
    "returnDate": "SEP 17",
    "returnDepTime": "14:30",
    "returnFlight": "WS2511",
    "returnArrTime": "03:12",
    "price": "$2170",
    "comment": ""
  }
]

## docker compose commands

docker-compose exec api python -c "
from app.routers.hotels import parse_location
print('Test 1:', parse_location('Paris, France (view the guide)'))
print('Test 2:', parse_location('New York, USA (travel tips)'))
print('Test 3:', parse_location('Tokyo, Japan'))
print('Test 4:', parse_location('London (great city), UK'))
"