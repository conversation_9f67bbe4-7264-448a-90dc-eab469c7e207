version: "3.9"

services:
  db:
    image: postgres:16
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_USER: postgres
      POSTGRES_DB: sirev
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d sirev"]
      interval: 5s
      timeout: 5s
      retries: 10

  api:
    build: .
    depends_on:
      db:
        condition: service_healthy
    env_file:
      - .env
    environment:
      DATABASE_URL: postgresql+asyncpg://postgres:postgres@db:5432/sirev
      PYTHONPATH: /app
    ports:
      - "8000:8000"
    volumes:
      - .:/app:delegated
    command: ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "5", "--worker-class", "gevent", "--worker-connections", "1000", "--timeout", "120", "--reload", "app.main:app"]

volumes:
  pgdata:
