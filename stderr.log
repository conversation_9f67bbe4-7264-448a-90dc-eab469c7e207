2025-09-10 21:32:13 ERROR [api.auth] Error in google_verify: 'generator' object has no attribute '__anext__'
2025-09-10 21:33:52 ERROR [api.auth] Error in google_verify: object NoneType can't be used in 'await' expression
2025-09-10 21:58:18 ERROR [api.auth] Error in google_verify: 'generator' object has no attribute '__anext__'
2025-09-10 21:59:18 ERROR [api.db] Database session error: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 21:59:18 ERROR [asyncio] Task exception was never retrieved
future: <Task finished name='Task-2' coro=<<async_generator_athrow without __name__>()> exception=IllegalStateChangeError("Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5>")>
Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 44, in get_session
    yield session
GeneratorExit

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 43, in get_session
    async with async_session() as session:
               ^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1080, in __aexit__
    await asyncio.shield(task)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1025, in close
    await greenlet_spawn(self.sync_session.close)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.switch(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2518, in close
    self._close_impl(invalidate=False)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2587, in _close_impl
    transaction.close(invalidate)
  File "<string>", line 2, in close
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/state_changes.py", line 121, in _go
    raise sa_exc.IllegalStateChangeError(
sqlalchemy.exc.IllegalStateChangeError: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 21:59:25 ERROR [sqlalchemy.pool.impl.AsyncAdaptedQueuePool] Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x7fa60a2c5400>>
Traceback (most recent call last):
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/pool/base.py", line 374, in _close_connection
    self._dialect.do_terminate(connection)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 1121, in do_terminate
    dbapi_connection.terminate()
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 899, in terminate
    self.await_(self._connection.close(timeout=2))
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/asyncpg/connection.py", line 1467, in close
    await self._protocol.close(timeout)
  File "asyncpg/protocol/protocol.pyx", line 626, in close
  File "asyncpg/protocol/protocol.pyx", line 659, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/asyncpg/connection.py", line 1611, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/alt/python312/lib64/python3.12/asyncio/base_events.py", line 455, in create_task
    self._check_closed()
  File "/opt/alt/python312/lib64/python3.12/asyncio/base_events.py", line 545, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py:-1: RuntimeWarning: coroutine 'Connection._cancel' was never awaited
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
2025-09-10 21:59:25 ERROR [api.auth] Error in google_verify: Task <Task pending name='Task-6' coro=<google_verify() running at /home/<USER>/proposal.toptravelagency.ca/app/controllers/auth.py:70> cb=[_run_until_complete_cb() at /opt/alt/python312/lib64/python3.12/asyncio/base_events.py:181]> got Future <Future pending cb=[Protocol._on_waiter_completed()]> attached to a different loop
2025-09-10 22:00:03 ERROR [api.db] Database session error: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 22:00:03 ERROR [asyncio] Task exception was never retrieved
future: <Task finished name='Task-12' coro=<<async_generator_athrow without __name__>()> exception=IllegalStateChangeError("Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5>")>
Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 44, in get_session
    yield session
GeneratorExit

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 43, in get_session
    async with async_session() as session:
               ^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1080, in __aexit__
    await asyncio.shield(task)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1025, in close
    await greenlet_spawn(self.sync_session.close)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.switch(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2518, in close
    self._close_impl(invalidate=False)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2587, in _close_impl
    transaction.close(invalidate)
  File "<string>", line 2, in close
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/state_changes.py", line 121, in _go
    raise sa_exc.IllegalStateChangeError(
sqlalchemy.exc.IllegalStateChangeError: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 22:00:30 INFO [api.hotels] /hotel-trips:bulk received 2 trips from user_id=2
2025-09-10 22:00:30 ERROR [sqlalchemy.pool.impl.AsyncAdaptedQueuePool] Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x7fa6094fb5c0>>
Traceback (most recent call last):
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/pool/base.py", line 374, in _close_connection
    self._dialect.do_terminate(connection)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 1121, in do_terminate
    dbapi_connection.terminate()
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 899, in terminate
    self.await_(self._connection.close(timeout=2))
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/asyncpg/connection.py", line 1467, in close
    await self._protocol.close(timeout)
  File "asyncpg/protocol/protocol.pyx", line 626, in close
  File "asyncpg/protocol/protocol.pyx", line 659, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/asyncpg/connection.py", line 1611, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/alt/python312/lib64/python3.12/asyncio/base_events.py", line 455, in create_task
    self._check_closed()
  File "/opt/alt/python312/lib64/python3.12/asyncio/base_events.py", line 545, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-09-10 22:00:30 ERROR [api.hotels] Error in submit_trips_bulk: Task <Task pending name='Task-16' coro=<submit_trips_bulk() running at /home/<USER>/proposal.toptravelagency.ca/app/controllers/hotels.py:200> cb=[_run_until_complete_cb() at /opt/alt/python312/lib64/python3.12/asyncio/base_events.py:181]> got Future <Future pending cb=[Protocol._on_waiter_completed()]> attached to a different loop
2025-09-10 22:00:41 INFO [api.hotels] /hotel-trips:bulk received 2 trips from user_id=2
2025-09-10 22:00:41 ERROR [api.db] Database session error: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 22:00:41 ERROR [asyncio] Task exception was never retrieved
future: <Task finished name='Task-22' coro=<<async_generator_athrow without __name__>()> exception=IllegalStateChangeError("Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5>")>
Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 44, in get_session
    yield session
GeneratorExit

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 43, in get_session
    async with async_session() as session:
               ^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1080, in __aexit__
    await asyncio.shield(task)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1025, in close
    await greenlet_spawn(self.sync_session.close)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.switch(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2518, in close
    self._close_impl(invalidate=False)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2587, in _close_impl
    transaction.close(invalidate)
  File "<string>", line 2, in close
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/state_changes.py", line 121, in _go
    raise sa_exc.IllegalStateChangeError(
sqlalchemy.exc.IllegalStateChangeError: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 22:00:41 ERROR [api.hotels] Error in submit_trips_bulk: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.InsufficientPrivilegeError'>: permission denied for sequence share_links_id_seq
[SQL: INSERT INTO share_links (user_id, token, title, note, is_active, expires_at) VALUES ($1::INTEGER, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::BOOLEAN, $6::TIMESTAMP WITHOUT TIME ZONE) RETURNING share_links.id, share_links.created_at]
[parameters: (2, 'MMOEknatUkFr3SF6w0vYtmLY', None, None, True, None)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-09-10 22:00:47 INFO [api.hotels] /hotel-trips:bulk received 2 trips from user_id=2
2025-09-10 22:00:47 ERROR [sqlalchemy.pool.impl.AsyncAdaptedQueuePool] Exception terminating connection <AdaptedConnection <asyncpg.connection.Connection object at 0x7fa60a066210>>
Traceback (most recent call last):
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/pool/base.py", line 374, in _close_connection
    self._dialect.do_terminate(connection)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 1121, in do_terminate
    dbapi_connection.terminate()
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/dialects/postgresql/asyncpg.py", line 899, in terminate
    self.await_(self._connection.close(timeout=2))
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/asyncpg/connection.py", line 1467, in close
    await self._protocol.close(timeout)
  File "asyncpg/protocol/protocol.pyx", line 626, in close
  File "asyncpg/protocol/protocol.pyx", line 659, in asyncpg.protocol.protocol.BaseProtocol._request_cancel
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/asyncpg/connection.py", line 1611, in _cancel_current_command
    self._cancellations.add(self._loop.create_task(self._cancel(waiter)))
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/alt/python312/lib64/python3.12/asyncio/base_events.py", line 455, in create_task
    self._check_closed()
  File "/opt/alt/python312/lib64/python3.12/asyncio/base_events.py", line 545, in _check_closed
    raise RuntimeError('Event loop is closed')
RuntimeError: Event loop is closed
2025-09-10 22:00:47 ERROR [api.hotels] Error in submit_trips_bulk: Task <Task pending name='Task-26' coro=<submit_trips_bulk() running at /home/<USER>/proposal.toptravelagency.ca/app/controllers/hotels.py:200> cb=[_run_until_complete_cb() at /opt/alt/python312/lib64/python3.12/asyncio/base_events.py:181]> got Future <Future pending cb=[Protocol._on_waiter_completed()]> attached to a different loop
2025-09-10 22:00:48 INFO [api.hotels] /hotel-trips:bulk received 2 trips from user_id=2
2025-09-10 22:00:48 ERROR [api.db] Database session error: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 22:00:48 ERROR [asyncio] Task exception was never retrieved
future: <Task finished name='Task-32' coro=<<async_generator_athrow without __name__>()> exception=IllegalStateChangeError("Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5>")>
Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 44, in get_session
    yield session
GeneratorExit

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 43, in get_session
    async with async_session() as session:
               ^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1080, in __aexit__
    await asyncio.shield(task)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1025, in close
    await greenlet_spawn(self.sync_session.close)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.switch(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2518, in close
    self._close_impl(invalidate=False)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2587, in _close_impl
    transaction.close(invalidate)
  File "<string>", line 2, in close
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/state_changes.py", line 121, in _go
    raise sa_exc.IllegalStateChangeError(
sqlalchemy.exc.IllegalStateChangeError: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 22:00:48 ERROR [api.hotels] Error in submit_trips_bulk: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.InsufficientPrivilegeError'>: permission denied for sequence share_links_id_seq
[SQL: INSERT INTO share_links (user_id, token, title, note, is_active, expires_at) VALUES ($1::INTEGER, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::BOOLEAN, $6::TIMESTAMP WITHOUT TIME ZONE) RETURNING share_links.id, share_links.created_at]
[parameters: (2, 'zAN5Vabxmrz8Ut80mOmiwhXj', None, None, True, None)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-09-10 22:03:07 INFO [api.hotels] /hotel-trips:bulk received 2 trips from user_id=2
2025-09-10 22:03:07 ERROR [api.db] Database session error: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 22:03:07 ERROR [asyncio] Task exception was never retrieved
future: <Task finished name='Task-2' coro=<<async_generator_athrow without __name__>()> exception=IllegalStateChangeError("Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5>")>
Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 44, in get_session
    yield session
GeneratorExit

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/proposal.toptravelagency.ca/app/db.py", line 43, in get_session
    async with async_session() as session:
               ^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1080, in __aexit__
    await asyncio.shield(task)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/ext/asyncio/session.py", line 1025, in close
    await greenlet_spawn(self.sync_session.close)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 190, in greenlet_spawn
    result = context.switch(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2518, in close
    self._close_impl(invalidate=False)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/session.py", line 2587, in _close_impl
    transaction.close(invalidate)
  File "<string>", line 2, in close
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib64/python3.12/site-packages/sqlalchemy/orm/state_changes.py", line 121, in _go
    raise sa_exc.IllegalStateChangeError(
sqlalchemy.exc.IllegalStateChangeError: Method 'close()' can't be called here; method '_connection_for_bind()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-09-10 22:03:07 ERROR [api.hotels] Error in submit_trips_bulk: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.InsufficientPrivilegeError'>: permission denied for sequence share_links_id_seq
[SQL: INSERT INTO share_links (user_id, token, title, note, is_active, expires_at) VALUES ($1::INTEGER, $2::VARCHAR, $3::VARCHAR, $4::VARCHAR, $5::BOOLEAN, $6::TIMESTAMP WITHOUT TIME ZONE) RETURNING share_links.id, share_links.created_at]
[parameters: (2, 'afFvt8n2GTic_EM2kGaMJGXG', None, None, True, None)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-09-10 23:37:56 INFO [api.hotels] /hotel-trips:bulk received 3 trips from user_id=2
2025-09-10 23:37:56 ERROR [api.hotels] Error in submit_trips_bulk: (psycopg2.errors.InsufficientPrivilege) permission denied for sequence share_links_id_seq

[SQL: INSERT INTO share_links (user_id, token, title, note, is_active, expires_at) VALUES (%(user_id)s, %(token)s, %(title)s, %(note)s, %(is_active)s, %(expires_at)s) RETURNING share_links.id, share_links.created_at]
[parameters: {'user_id': 2, 'token': 'WM5uZwBg-hnLTOWk9LTzGBep', 'title': None, 'note': None, 'is_active': True, 'expires_at': None}]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-09-10 23:38:03 INFO [api.hotels] /hotel-trips:bulk received 3 trips from user_id=2
2025-09-10 23:38:03 ERROR [api.hotels] Error in submit_trips_bulk: (psycopg2.errors.InsufficientPrivilege) permission denied for sequence share_links_id_seq

[SQL: INSERT INTO share_links (user_id, token, title, note, is_active, expires_at) VALUES (%(user_id)s, %(token)s, %(title)s, %(note)s, %(is_active)s, %(expires_at)s) RETURNING share_links.id, share_links.created_at]
[parameters: {'user_id': 2, 'token': 'P1Q1gUJXIcVvEWi_2-fUW_oQ', 'title': None, 'note': None, 'is_active': True, 'expires_at': None}]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-09-10 23:43:21 INFO [api.hotels] /hotel-trips:bulk received 3 trips from user_id=2
2025-09-10 23:43:21 ERROR [api.hotels] Error in submit_trips_bulk: (psycopg2.errors.InsufficientPrivilege) permission denied for sequence share_links_id_seq

[SQL: INSERT INTO share_links (user_id, token, title, note, is_active, expires_at) VALUES (%(user_id)s, %(token)s, %(title)s, %(note)s, %(is_active)s, %(expires_at)s) RETURNING share_links.id, share_links.created_at]
[parameters: {'user_id': 2, 'token': 'yuBKsQUaUiQCpjD_-CFEaJ2_', 'title': None, 'note': None, 'is_active': True, 'expires_at': None}]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-09-10 23:48:29 INFO [api.hotels] /hotel-trips:bulk received 3 trips from user_id=2
2025-09-10 23:48:29 INFO [tripadvisor] TripAdvisor client initialized with base_url: https://api.content.tripadvisor.com
2025-09-10 23:48:29 INFO [tripadvisor] API key present: True
2025-09-10 23:48:29 INFO [tripadvisor] API key (first 8 chars): A21A0C3E...
2025-09-10 23:48:29 INFO [api.hotels] Hotel not found locally. Searching TripAdvisor name=Tukan Hotel Playa Del Carmen location=Riviera Maya, Mexico
2025-09-10 23:48:29 INFO [tripadvisor] TripAdvisor GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en params={'searchQuery': 'Tukan Hotel Playa Del Carmen, Riviera Maya, Mexico', 'key': 'A21A0C3E90C34992A3C59B4D1382C2BE'}
2025-09-10 23:48:30 INFO [httpx] HTTP Request: GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en&searchQuery=Tukan%20Hotel%20Playa%20Del%20Carmen%2C%20Riviera%20Maya%2C%20Mexico&key=A21A0C3E90C34992A3C59B4D1382C2BE "HTTP/1.1 403 Forbidden"
2025-09-10 23:48:30 INFO [tripadvisor] TripAdvisor RESP 403 https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en {
  "Message": "User is not authorized to access this resource with an explicit deny"
}
2025-09-10 23:48:30 WARNING [tripadvisor] TripAdvisor search returned status 403 for Tukan Hotel Playa Del Carmen
2025-09-10 23:48:30 WARNING [api.hotels] TripAdvisor search returned no results for Tukan Hotel Playa Del Carmen / Riviera Maya, Mexico
2025-09-10 23:48:30 INFO [api.hotels] Hotel not found locally. Searching TripAdvisor name=Hm Playa Del Carmen location=Riviera Maya, Mexico
2025-09-10 23:48:30 INFO [tripadvisor] TripAdvisor GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en params={'searchQuery': 'Hm Playa Del Carmen, Riviera Maya, Mexico', 'key': 'A21A0C3E90C34992A3C59B4D1382C2BE'}
2025-09-10 23:48:30 INFO [httpx] HTTP Request: GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en&searchQuery=Hm%20Playa%20Del%20Carmen%2C%20Riviera%20Maya%2C%20Mexico&key=A21A0C3E90C34992A3C59B4D1382C2BE "HTTP/1.1 403 Forbidden"
2025-09-10 23:48:30 INFO [tripadvisor] TripAdvisor RESP 403 https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en {
  "Message": "User is not authorized to access this resource with an explicit deny"
}
2025-09-10 23:48:30 WARNING [tripadvisor] TripAdvisor search returned status 403 for Hm Playa Del Carmen
2025-09-10 23:48:30 WARNING [api.hotels] TripAdvisor search returned no results for Hm Playa Del Carmen / Riviera Maya, Mexico
2025-09-10 23:48:30 INFO [api.hotels] Found existing hotel id=4 for Tukan Hotel Playa Del Carmen
2025-09-10 23:48:30 INFO [api.hotels] Created share link 1C5t1Kx9DTP84vtiBz5K59p7 with 3 trips
[ERROR] [UID:1466][2525051] wsgiAppHandler pApp->start_response() return NULL.
Traceback (most recent call last):
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/app.py", line 867, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/app.py", line 841, in dispatch_request
    self.raise_routing_exception(req)
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/app.py", line 450, in raise_routing_exception
    raise request.routing_exception  # type: ignore
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/ctx.py", line 353, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/werkzeug/routing/map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/app.py", line 1478, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/app.py", line 1458, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask_cors/extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/app.py", line 1455, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/app.py", line 869, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask_cors/extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/flask/app.py", line 759, in handle_user_exception
    return self.ensure_sync(handler)(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/virtualenv/proposal.toptravelagency.ca/3.12/lib/python3.12/site-packages/connexion/apps/flask.py", line 245, in _http_exception
    raise starlette.exceptions.HTTPException(exc.code, detail=exc.description)
starlette.exceptions.HTTPException: 404: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-09-10 23:52:32 INFO [api.hotels] /hotel-trips:bulk received 4 trips from user_id=2
2025-09-10 23:52:32 INFO [tripadvisor] TripAdvisor client initialized with base_url: https://api.content.tripadvisor.com
2025-09-10 23:52:32 INFO [tripadvisor] API key present: True
2025-09-10 23:52:32 INFO [tripadvisor] API key (first 8 chars): A21A0C3E...
2025-09-10 23:52:32 INFO [api.hotels] Hotel not found locally. Searching TripAdvisor name=Allegro Playacar location=Riviera Maya, Mexico
2025-09-10 23:52:32 INFO [tripadvisor] TripAdvisor GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en params={'searchQuery': 'Allegro Playacar, Riviera Maya, Mexico', 'key': 'A21A0C3E90C34992A3C59B4D1382C2BE'}
2025-09-10 23:52:33 INFO [httpx] HTTP Request: GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en&searchQuery=Allegro%20Playacar%2C%20Riviera%20Maya%2C%20Mexico&key=A21A0C3E90C34992A3C59B4D1382C2BE "HTTP/1.1 403 Forbidden"
2025-09-10 23:52:33 INFO [tripadvisor] TripAdvisor RESP 403 https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en {
  "Message": "User is not authorized to access this resource with an explicit deny"
}
2025-09-10 23:52:33 WARNING [tripadvisor] TripAdvisor search returned status 403 for Allegro Playacar
2025-09-10 23:52:33 WARNING [api.hotels] TripAdvisor search returned no results for Allegro Playacar / Riviera Maya, Mexico
2025-09-10 23:52:33 INFO [api.hotels] Found existing hotel id=6 for Allegro Playacar
2025-09-10 23:52:33 INFO [api.hotels] Found existing hotel id=6 for Allegro Playacar
2025-09-10 23:52:33 INFO [api.hotels] Hotel not found locally. Searching TripAdvisor name=Catalonia Riviera Maya location=Riviera Maya, Mexico
2025-09-10 23:52:33 INFO [tripadvisor] TripAdvisor GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en params={'searchQuery': 'Catalonia Riviera Maya, Riviera Maya, Mexico', 'key': 'A21A0C3E90C34992A3C59B4D1382C2BE'}
2025-09-10 23:52:33 INFO [httpx] HTTP Request: GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en&searchQuery=Catalonia%20Riviera%20Maya%2C%20Riviera%20Maya%2C%20Mexico&key=A21A0C3E90C34992A3C59B4D1382C2BE "HTTP/1.1 403 Forbidden"
2025-09-10 23:52:33 INFO [tripadvisor] TripAdvisor RESP 403 https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en {
  "Message": "User is not authorized to access this resource with an explicit deny"
}
2025-09-10 23:52:33 WARNING [tripadvisor] TripAdvisor search returned status 403 for Catalonia Riviera Maya
2025-09-10 23:52:33 WARNING [api.hotels] TripAdvisor search returned no results for Catalonia Riviera Maya / Riviera Maya, Mexico
2025-09-10 23:52:33 INFO [api.hotels] Created share link yatl6mGu5tbUEoEGl3uG4FRX with 4 trips
2025-09-10 23:54:58 INFO [api.hotels] /hotel-trips:bulk received 4 trips from user_id=2
2025-09-10 23:54:58 INFO [tripadvisor] TripAdvisor client initialized with base_url: https://api.content.tripadvisor.com
2025-09-10 23:54:58 INFO [tripadvisor] API key present: True
2025-09-10 23:54:58 INFO [tripadvisor] API key (first 8 chars): A21A0C3E...
2025-09-10 23:54:58 INFO [api.hotels] Found existing hotel id=6 for Allegro Playacar
2025-09-10 23:54:58 INFO [api.hotels] Found existing hotel id=6 for Allegro Playacar
2025-09-10 23:54:58 INFO [api.hotels] Found existing hotel id=6 for Allegro Playacar
2025-09-10 23:54:58 INFO [api.hotels] Found existing hotel id=7 for Catalonia Riviera Maya
2025-09-10 23:54:58 INFO [api.hotels] Created share link 5WXEvbn-Qpv4o8uQciYGpLN6 with 4 trips
2025-09-10 23:56:07 INFO [api.hotels] /hotel-trips:bulk received 4 trips from user_id=2
2025-09-10 23:56:07 INFO [tripadvisor] TripAdvisor client initialized with base_url: https://api.content.tripadvisor.com
2025-09-10 23:56:07 INFO [tripadvisor] API key present: True
2025-09-10 23:56:07 INFO [tripadvisor] API key (first 8 chars): A21A0C3E...
2025-09-10 23:56:07 INFO [api.hotels] Hotel not found locally. Searching TripAdvisor name=Allegro Playacar location=Riviera Maya, Mexico
2025-09-10 23:56:07 INFO [tripadvisor] TripAdvisor GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en params={'searchQuery': 'Allegro Playacar, Riviera Maya, Mexico', 'key': 'A21A0C3E90C34992A3C59B4D1382C2BE'}
2025-09-10 23:56:08 INFO [httpx] HTTP Request: GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en&searchQuery=Allegro%20Playacar%2C%20Riviera%20Maya%2C%20Mexico&key=A21A0C3E90C34992A3C59B4D1382C2BE "HTTP/1.1 403 Forbidden"
2025-09-10 23:56:08 INFO [tripadvisor] TripAdvisor RESP 403 https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en {
  "Message": "User is not authorized to access this resource with an explicit deny"
}
2025-09-10 23:56:08 WARNING [tripadvisor] TripAdvisor search returned status 403 for Allegro Playacar
2025-09-10 23:56:08 WARNING [api.hotels] TripAdvisor search returned no results for Allegro Playacar / Riviera Maya, Mexico
2025-09-10 23:56:08 INFO [api.hotels] Found existing hotel id=8 for Allegro Playacar
2025-09-10 23:56:08 INFO [api.hotels] Found existing hotel id=8 for Allegro Playacar
2025-09-10 23:56:08 INFO [api.hotels] Hotel not found locally. Searching TripAdvisor name=Catalonia Riviera Maya location=Riviera Maya, Mexico
2025-09-10 23:56:08 INFO [tripadvisor] TripAdvisor GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en params={'searchQuery': 'Catalonia Riviera Maya, Riviera Maya, Mexico', 'key': 'A21A0C3E90C34992A3C59B4D1382C2BE'}
2025-09-10 23:56:08 INFO [httpx] HTTP Request: GET https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en&searchQuery=Catalonia%20Riviera%20Maya%2C%20Riviera%20Maya%2C%20Mexico&key=A21A0C3E90C34992A3C59B4D1382C2BE "HTTP/1.1 403 Forbidden"
2025-09-10 23:56:08 INFO [tripadvisor] TripAdvisor RESP 403 https://api.content.tripadvisor.com/api/v1/location/search?category=hotels&language=en {
  "Message": "User is not authorized to access this resource with an explicit deny"
}
2025-09-10 23:56:08 WARNING [tripadvisor] TripAdvisor search returned status 403 for Catalonia Riviera Maya
2025-09-10 23:56:08 WARNING [api.hotels] TripAdvisor search returned no results for Catalonia Riviera Maya / Riviera Maya, Mexico
2025-09-10 23:56:08 INFO [api.hotels] Created share link J238tJIS-NKFwdWZyvJxXdG0 with 4 trips

curl -sS https://requestmirror.dev/api/v1 | jq