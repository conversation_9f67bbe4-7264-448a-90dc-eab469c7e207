---
deployment:
  tasks:
    # Install Python dependencies
    - export PYTHONPATH=/home/<USER>/public_html:$PYTHONPATH
    - /usr/local/bin/pip3.11 install --user -r requirements.txt
    
    # Run database migrations (optional - you might want to do this manually)
    # - /usr/local/bin/python3.11 -m alembic upgrade head
    
    # Set proper permissions
    - chmod 644 passenger_wsgi.py
    - find . -type f -name "*.py" -exec chmod 644 {} \;
    - find . -type d -exec chmod 755 {} \;
